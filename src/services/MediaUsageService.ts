/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { DonationManager } from '#src/lib/donations/core/DonationManager.js';
import { getRedis } from '#src/utils/Redis.js';
import { RedisKeys } from '#utils/Constants.js';
import Logger from '#utils/Logger.js';
import type { Redis } from 'ioredis';

// Media types for Redis-based tracking
export type MediaType = 'GIF' | 'STICKER' | 'IMAGE';

export interface MediaUsageStats {
  totalUsed: number;
  remaining: number;
  hasUnlimitedAccess: boolean;
  canUseMedia: boolean;
}

export interface MediaUsageResult {
  success: boolean;
  message: string;
  remainingCount?: number;
  requiresTopggVote?: boolean;
  requiresPremium?: boolean;
  canVoteOnTopgg?: boolean;
}

export interface MediaUsageLog {
  callId: string;
  userId: string;
  mediaType: MediaType;
  mediaUrl: string;
  usedAt: string;
}

/**
 * Service for managing media usage in calls
 * Handles tracking, limits, and premium access using Redis for performance
 */
export default class MediaUsageService {
  private readonly redis: Redis;
  private readonly donationManager: DonationManager;
  private readonly INITIAL_MEDIA_LIMIT = 2; // Initial allowance per call
  private readonly VOTED_MEDIA_LIMIT = 7; // Total limit after Top.gg vote (2 + 5)
  private readonly CALL_SESSION_TTL = 24 * 60 * 60; // 24 hours in seconds
  private readonly VOTE_COOLDOWN_TTL = 12 * 60 * 60; // 12 hours in seconds

  constructor() {
    this.redis = getRedis();
    this.donationManager = new DonationManager();
  }

  /**
   * Get media usage statistics for a user in a specific call
   */
  async getMediaUsageStats(callId: string, userId: string): Promise<MediaUsageStats> {
    try {
      // Use Redis pipeline for better performance
      const pipeline = this.redis.pipeline();

      // Check user's premium status using DonationManager
      const hasUnlimitedAccess = await this.donationManager.hasMediaPremium(userId);

      if (hasUnlimitedAccess) {
        return {
          totalUsed: 0,
          remaining: Infinity,
          hasUnlimitedAccess: true,
          canUseMedia: true,
        };
      }

      // Get usage count and vote status in parallel using pipeline
      const usageCountKey = `${RedisKeys.MediaUsageCount}:${callId}:${userId}`;
      const voteCooldownKey = `${RedisKeys.MediaVoteCooldown}:${userId}`;

      pipeline.get(usageCountKey);
      pipeline.exists(voteCooldownKey);

      const results = await pipeline.exec();

      if (!results) {
        throw new Error('Redis pipeline execution failed');
      }

      const usageCountStr = results[0]?.[1] as string | null;
      const hasRecentTopggVote = (results[1]?.[1] as number) === 1;

      const usageCount = usageCountStr ? parseInt(usageCountStr, 10) : 0;
      const limit = hasRecentTopggVote ? this.VOTED_MEDIA_LIMIT : this.INITIAL_MEDIA_LIMIT;
      const remaining = Math.max(0, limit - usageCount);

      return {
        totalUsed: usageCount,
        remaining,
        hasUnlimitedAccess: false,
        canUseMedia: remaining > 0,
      };
    }
    catch (error) {
      Logger.error('Error getting media usage stats:', error);
      // Return conservative stats on error
      return {
        totalUsed: this.INITIAL_MEDIA_LIMIT,
        remaining: 0,
        hasUnlimitedAccess: false,
        canUseMedia: false,
      };
    }
  }

  /**
   * Attempt to use media in a call
   */
  async useMedia(
    callId: string,
    userId: string,
    mediaType: MediaType,
    mediaUrl: string,
  ): Promise<MediaUsageResult> {
    try {
      const stats = await this.getMediaUsageStats(callId, userId);

      if (stats.hasUnlimitedAccess) {
        // Premium users can always use media - just log the usage in Redis
        await this.logMediaUsage(callId, userId, mediaType, mediaUrl);
        return {
          success: true,
          message: 'Media shared successfully! (Premium)',
        };
      }

      if (!stats.canUseMedia) {
        // Check if they've used their initial allowance
        if (stats.totalUsed >= this.INITIAL_MEDIA_LIMIT) {
          // Check if user can vote on Top.gg (hasn't voted in last 12 hours)
          const voteCooldownKey = `${RedisKeys.MediaVoteCooldown}:${userId}`;
          const canVoteOnTopgg = !(await this.redis.exists(voteCooldownKey));

          if (canVoteOnTopgg) {
            return {
              success: false,
              message:
                'Media limit reached. Vote for InterChat on Top.gg to get 5 more media items!',
              requiresTopggVote: true,
              requiresPremium: true,
              canVoteOnTopgg: true,
            };
          }
          else {
            const ttl = await this.redis.ttl(voteCooldownKey);
            const timeUntilNextVote = Math.ceil(ttl / 3600); // Convert seconds to hours
            return {
              success: false,
              message: `Media limit reached. You can vote again on Top.gg in ${timeUntilNextVote} hours or upgrade to premium.`,
              requiresTopggVote: false,
              requiresPremium: true,
              canVoteOnTopgg: false,
            };
          }
        }

        return {
          success: false,
          message: 'Media limit reached.',
          requiresPremium: true,
        };
      }

      // Use pipeline for atomic operations
      const usageCountKey = `${RedisKeys.MediaUsageCount}:${callId}:${userId}`;
      const pipeline = this.redis.pipeline();

      // Increment usage count and set TTL atomically
      pipeline.incr(usageCountKey);
      pipeline.expire(usageCountKey, this.CALL_SESSION_TTL);

      // Execute Redis operations and log media usage
      await Promise.all([
        pipeline.exec(),
        this.logMediaUsage(callId, userId, mediaType, mediaUrl),
      ]);

      const newRemaining = stats.remaining - 1;
      return {
        success: true,
        message: `Media shared successfully! ${newRemaining} remaining.`,
        remainingCount: newRemaining,
      };
    }
    catch (error) {
      Logger.error('Error using media:', error);
      return {
        success: false,
        message: 'Failed to share media. Please try again.',
      };
    }
  }

  /**
   * Reset media usage for a new call session
   */
  async resetCallMediaUsage(callId: string): Promise<void> {
    try {
      // Clear all usage count entries for this call using Lua script for better performance
      const luaScript = `
        local keys = redis.call('KEYS', ARGV[1])
        if #keys > 0 then
          return redis.call('DEL', unpack(keys))
        end
        return 0
      `;

      const pattern = `${RedisKeys.MediaUsageCount}:${callId}:*`;
      const deletedCount = await this.redis.eval(luaScript, 0, pattern) as number;

      Logger.debug(`Reset ${deletedCount} media usage counts for call ${callId}`);
    }
    catch (error) {
      Logger.error('Error resetting call media usage:', error);
    }
  }

  /**
   * Log media usage in Redis for analytics/debugging
   */
  private async logMediaUsage(
    callId: string,
    userId: string,
    mediaType: MediaType,
    mediaUrl: string,
  ): Promise<void> {
    try {
      // Store media usage log in Redis with TTL for cleanup
      const logKey = `${RedisKeys.MediaUsageCount}:log:${callId}:${userId}:${Date.now()}`;
      const logData = JSON.stringify({
        callId,
        userId,
        mediaType,
        mediaUrl,
        usedAt: new Date().toISOString(),
      });

      // Store log with 7-day TTL for debugging purposes
      await this.redis.setex(logKey, 7 * 24 * 60 * 60, logData);

      Logger.debug(`Logged media usage: ${mediaType} for user ${userId} in call ${callId}`);
    }
    catch (error) {
      Logger.error('Error logging media usage:', error);
      // Don't throw - logging failure shouldn't break media sharing
    }
  }

  /**
   * Handle Top.gg vote completion - set vote cooldown in Redis
   * This should be called when a Top.gg vote webhook is received
   */
  async handleTopggVote(userId: string): Promise<void> {
    try {
      // Set vote cooldown in Redis (12 hours)
      const voteCooldownKey = `${RedisKeys.MediaVoteCooldown}:${userId}`;
      await this.redis.setex(voteCooldownKey, this.VOTE_COOLDOWN_TTL, '1');

      Logger.info(`Set media vote cooldown for user ${userId} after Top.gg vote`);
    }
    catch (error) {
      Logger.error('Error handling Top.gg vote for media usage:', error);
    }
  }

  /**
   * Check if user can vote on Top.gg for additional media
   */
  async canUserVoteOnTopgg(userId: string): Promise<boolean> {
    try {
      const voteCooldownKey = `${RedisKeys.MediaVoteCooldown}:${userId}`;
      const exists = await this.redis.exists(voteCooldownKey);
      return !exists; // Can vote if cooldown key doesn't exist
    }
    catch (error) {
      Logger.error('Error checking Top.gg vote eligibility:', error);
      return false;
    }
  }

  /**
   * Initialize media usage tracking for a new call
   */
  async initializeCallMediaTracking(callId: string): Promise<void> {
    try {
      // Set a session marker to track call initialization
      const sessionKey = `${RedisKeys.MediaSessionReset}:${callId}`;
      await this.redis.setex(sessionKey, this.CALL_SESSION_TTL, '1');

      Logger.debug(`Initialized media tracking for call ${callId}`);
    }
    catch (error) {
      Logger.error('Error initializing call media tracking:', error);
    }
  }

  /**
   * Get media usage history for a call from Redis logs
   */
  async getCallMediaHistory(callId: string): Promise<MediaUsageLog[]> {
    try {
      // Get all log keys for this call
      const pattern = `${RedisKeys.MediaUsageCount}:log:${callId}:*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length === 0) {
        return [];
      }

      // Get all log data
      const logs = await this.redis.mget(...keys);

      // Parse and sort logs
      const parsedLogs: MediaUsageLog[] = logs
        .filter((log): log is string => log !== null)
        .map((log) => JSON.parse(log))
        .sort((a, b) => new Date(b.usedAt).getTime() - new Date(a.usedAt).getTime());

      return parsedLogs;
    }
    catch (error) {
      Logger.error('Error getting call media history:', error);
      return [];
    }
  }
}
