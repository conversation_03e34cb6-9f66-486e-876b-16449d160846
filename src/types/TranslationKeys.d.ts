/*
  THIS IS AN AUTOGENERATED FILE. DO NOT EDIT IT DIRECTLY.
  To regenerate this file, run 'npm run locale-types'.
*/

export type TranslationKeys = {
  'rules.header': never;
  'rules.botRulesNote': never;
  'rules.rules':
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link'
    | 'guidelines_link';
  'rules.welcome': 'emoji' | 'user';
  'rules.alreadyAccepted': 'emoji' | 'user';
  'rules.continue': never;
  'rules.accept': never;
  'rules.decline': never;
  'rules.agreementNote': never;
  'rules.hubAgreementNote': never;
  'rules.accepted': 'emoji' | 'hubs_link' | 'dashboard_link' | 'support_invite' | 'donateLink';
  'rules.declined': 'emoji';
  'rules.hubAccepted': 'emoji';
  'rules.hubDeclined': 'emoji' | 'hubName';
  'rules.noHubRules': 'rules_link';
  'rules.hubRules': never;
  'rules.viewbotRules': never;
  'vote.description': never;
  'vote.footer': never;
  'vote.button.label': never;
  'vote.perks.moreComingSoon': 'support_invite';
  'vote.fields.currentStreak': never;
  'vote.fields.lastVote': never;
  'vote.fields.voterPerks': never;
  'vote.fields.voteNow': 'vote_url';
  'vote.fields.perks.messageLength': never;
  'vote.fields.perks.stickers': never;
  'vote.fields.perks.createHubs': never;
  'vote.fields.perks.welcomeMessages': never;
  'vote.fields.perks.voterRole': never;
  'vote.fields.perks.voterBadge': never;
  'vote.embed.title': never;
  'network.accountTooNew': 'emoji' | 'user';
  'network.deleteSuccess': 'emoji' | 'user' | 'deleted' | 'total';
  'network.editInProgress': 'emoji';
  'network.editInProgressError': 'emoji';
  'network.emptyContent': 'emoji';
  'network.newMessageContent': never;
  'network.editMessagePrompt': 'emoji';
  'network.editSuccess': 'emoji' | 'user' | 'edited' | 'total';
  'network.onboarding.welcome.title': never;
  'network.onboarding.welcome.description': never;
  'network.onboarding.embed.title': 'hubName';
  'network.onboarding.embed.description': never;
  'network.onboarding.embed.footer': never;
  'network.onboarding.inProgress': 'emoji' | 'channel';
  'blacklist.description': never;
  'blacklist.success': 'emoji' | 'name';
  'blacklist.removed': 'emoji' | 'name';
  'blacklist.modal.reason.label': never;
  'blacklist.modal.reason.placeholder': never;
  'blacklist.modal.duration.label': never;
  'blacklist.modal.duration.placeholder': never;
  'blacklist.user.description': never;
  'blacklist.user.options.user.description': never;
  'blacklist.user.options.hub.description': never;
  'blacklist.user.selectDuration': 'username';
  'blacklist.user.cannotBlacklistMod': 'emoji';
  'blacklist.user.alreadyBlacklisted': 'emoji';
  'blacklist.user.easterEggs.blacklistBot': never;
  'blacklist.server.description': never;
  'blacklist.server.options.server.description': never;
  'blacklist.server.options.hub.description': never;
  'blacklist.server.selectDuration': 'serverName';
  'blacklist.server.alreadyBlacklisted': 'emoji';
  'blacklist.server.unknownError': 'server';
  'blacklist.list.description': never;
  'blacklist.list.user': 'id' | 'moderator' | 'reason' | 'expires';
  'blacklist.list.server': 'id' | 'moderator' | 'reason' | 'expires';
  'msgInfo.buttons.message': never;
  'msgInfo.buttons.server': never;
  'msgInfo.buttons.user': never;
  'msgInfo.buttons.report': never;
  'msgInfo.report.notEnabled': 'emoji';
  'msgInfo.report.success': 'emoji';
  invite: 'invite_emoji' | 'invite' | 'support_emoji' | 'support';
  'connection.joinRequestsDisabled': 'emoji';
  'connection.notFound': 'emoji';
  'connection.channelNotFound': 'emoji';
  'connection.alreadyConnected': 'emoji' | 'channel';
  'connection.switchChannel': 'emoji';
  'connection.switchCalled': 'emoji';
  'connection.switchSuccess': 'emoji' | 'channel';
  'connection.inviteRemoved': 'emoji';
  'connection.setInviteError': 'emoji';
  'connection.inviteAdded': 'emoji';
  'connection.emColorInvalid': 'emoji';
  'connection.emColorChange': 'emoji' | 'action';
  'connection.embed.title': never;
  'connection.embed.fields.hub': never;
  'connection.embed.fields.channel': never;
  'connection.embed.fields.invite': never;
  'connection.embed.fields.connected': never;
  'connection.embed.fields.emColor': never;
  'connection.embed.fields.compact': never;
  'connection.embed.footer': never;
  'connection.selects.placeholder': never;
  'connection.unpaused.desc': 'tick_emoji' | 'channel';
  'connection.unpaused.tips': 'pause_cmd' | 'edit_cmd';
  'connection.paused.desc': 'clock_emoji' | 'channel';
  'connection.paused.tips': 'unpause_cmd' | 'leave_cmd';
  'hub.notFound': 'emoji' | 'hubs_link';
  'hub.notFound_mod': 'emoji';
  'hub.notManager': 'emoji';
  'hub.notModerator': 'emoji';
  'hub.notPrivate': 'emoji';
  'hub.notOwner': 'emoji';
  'hub.alreadyJoined': 'emoji' | 'hub' | 'channel';
  'hub.invalidChannel': 'emoji';
  'hub.invalidImgurUrl': 'emoji';
  'hub.join.success': 'hub' | 'channel';
  'hub.join.nsfwChannelSfwHub': 'emoji' | 'channel' | 'hub';
  'hub.join.sfwChannelNsfwHub': 'emoji' | 'channel' | 'hub';
  'hub.servers.total': 'from' | 'to' | 'total';
  'hub.servers.noConnections': 'emoji';
  'hub.servers.notConnected': 'emoji' | 'hub';
  'hub.servers.connectionInfo':
    | 'serverId'
    | 'channelName'
    | 'channelId'
    | 'joinedAt'
    | 'invite'
    | 'connected';
  'hub.blockwords.deleted': 'emoji';
  'hub.blockwords.notFound': 'emoji';
  'hub.blockwords.maxRules': 'emoji';
  'hub.blockwords.configure': 'rule';
  'hub.blockwords.actionsUpdated': 'emoji' | 'actions';
  'hub.blockwords.selectRuleToEdit': never;
  'hub.blockwords.listDescription': 'emoji' | 'totalRules';
  'hub.blockwords.listFooter': never;
  'hub.blockwords.ruleDescription': 'emoji' | 'ruleName' | 'words';
  'hub.blockwords.ruleFooter': never;
  'hub.blockwords.actionSelectPlaceholder': never;
  'hub.blockwords.embedFields.noActions': 'emoji';
  'hub.blockwords.embedFields.actionsName': never;
  'hub.blockwords.embedFields.actionsValue': 'actions';
  'hub.blockwords.modal.addRule': never;
  'hub.blockwords.modal.editingRule': never;
  'hub.blockwords.modal.ruleNameLabel': never;
  'hub.blockwords.modal.wordsLabel': never;
  'hub.blockwords.modal.wordsPlaceholder': never;
  'hub.blockwords.validating': 'emoji';
  'hub.blockwords.noRules': 'emoji';
  'hub.create.modal.title': never;
  'hub.create.modal.name.label': never;
  'hub.create.modal.name.placeholder': never;
  'hub.create.modal.description.label': never;
  'hub.create.modal.description.placeholder': never;
  'hub.create.modal.icon.label': never;
  'hub.create.modal.icon.placeholder': never;
  'hub.create.modal.banner.label': never;
  'hub.create.modal.banner.placeholder': never;
  'hub.create.maxHubs': 'emoji' | 'voteUrl' | 'maxHubs';
  'hub.create.invalidName': 'emoji';
  'hub.create.nameTaken': 'emoji';
  'hub.create.success': 'name' | 'support_invite' | 'donateLink';
  'hub.delete.confirm': 'hub';
  'hub.delete.ownerOnly': 'emoji';
  'hub.delete.success': 'emoji' | 'hub';
  'hub.delete.cancelled': 'emoji';
  'hub.invite.create.success': 'inviteCode' | 'expiry' | 'inviteCode';
  'hub.invite.revoke.invalidCode': 'emoji';
  'hub.invite.revoke.success': 'emoji' | 'inviteCode';
  'hub.invite.list.title': never;
  'hub.invite.list.noInvites': 'emoji';
  'hub.invite.list.notPrivate': 'emoji';
  'hub.joined.noJoinedHubs': 'emoji' | 'hubs_link';
  'hub.joined.joinedHubs': 'total';
  'hub.leave.noHub': 'emoji';
  'hub.leave.confirm': 'hub' | 'channel';
  'hub.leave.confirmFooter': never;
  'hub.leave.success': 'emoji' | 'channel';
  'hub.moderator.noModerators': 'emoji';
  'hub.moderator.add.success': 'emoji' | 'user' | 'position';
  'hub.moderator.add.alreadyModerator': 'emoji' | 'user';
  'hub.moderator.remove.success': 'emoji' | 'user';
  'hub.moderator.remove.notModerator': 'emoji' | 'user';
  'hub.moderator.remove.notOwner': 'emoji';
  'hub.moderator.update.success': 'emoji' | 'user' | 'position';
  'hub.moderator.update.notModerator': 'emoji' | 'user';
  'hub.moderator.update.notAllowed': 'emoji';
  'hub.moderator.update.notOwner': 'emoji';
  'hub.manage.dashboardTip': 'url';
  'hub.manage.enterImgurUrl': never;
  'hub.manage.icon.changed': never;
  'hub.manage.icon.modal.title': never;
  'hub.manage.icon.modal.label': never;
  'hub.manage.icon.selects.label': never;
  'hub.manage.icon.selects.description': never;
  'hub.manage.description.changed': never;
  'hub.manage.description.modal.title': never;
  'hub.manage.description.modal.label': never;
  'hub.manage.description.modal.placeholder': never;
  'hub.manage.description.selects.label': never;
  'hub.manage.description.selects.description': never;
  'hub.manage.banner.changed': never;
  'hub.manage.banner.removed': never;
  'hub.manage.banner.modal.title': never;
  'hub.manage.banner.modal.label': never;
  'hub.manage.banner.selects.label': never;
  'hub.manage.banner.selects.description': never;
  'hub.manage.visibility.success': 'emoji' | 'visibility';
  'hub.manage.visibility.selects.label': never;
  'hub.manage.visibility.selects.description': never;
  'hub.manage.toggleLock.selects.label': never;
  'hub.manage.toggleLock.selects.description': never;
  'hub.manage.toggleLock.confirmation': 'status';
  'hub.manage.toggleLock.announcementTitle': 'status';
  'hub.manage.toggleLock.announcementDescription.locked': never;
  'hub.manage.toggleLock.announcementDescription.unlocked': never;
  'hub.manage.toggleNsfw.modal.title': never;
  'hub.manage.toggleNsfw.modal.label': never;
  'hub.manage.toggleNsfw.modal.description': never;
  'hub.manage.toggleNsfw.selects.label': never;
  'hub.manage.toggleNsfw.selects.description': never;
  'hub.manage.toggleNsfw.confirmation': 'status';
  'hub.manage.toggleNsfw.announcementTitle': 'status';
  'hub.manage.toggleNsfw.announcementDescription.nsfw': never;
  'hub.manage.toggleNsfw.announcementDescription.sfw': never;
  'hub.manage.setNsfw.success': 'emoji' | 'hub' | 'status';
  'hub.manage.setNsfw.announcement': 'emoji' | 'status' | 'description';
  'hub.manage.nsfwAlreadySet': 'emoji' | 'hub' | 'status';
  'hub.manage.embed.visibility': never;
  'hub.manage.embed.connections': never;
  'hub.manage.embed.chatsLocked': never;
  'hub.manage.embed.blacklists': never;
  'hub.manage.embed.total': never;
  'hub.manage.embed.users': never;
  'hub.manage.embed.servers': never;
  'hub.manage.embed.hubStats': never;
  'hub.manage.embed.moderators': never;
  'hub.manage.embed.owner': never;
  'hub.manage.logs.title': never;
  'hub.manage.logs.reset': 'emoji' | 'type';
  'hub.manage.logs.roleSuccess': 'emoji' | 'type' | 'role';
  'hub.manage.logs.roleRemoved': 'emoji' | 'type';
  'hub.manage.logs.channelSuccess': 'emoji' | 'type' | 'channel';
  'hub.manage.logs.channelSelect': never;
  'hub.manage.logs.roleSelect': never;
  'hub.manage.logs.reportChannelFirst': 'emoji';
  'hub.manage.logs.config.title': 'type';
  'hub.manage.logs.config.description': 'arrow' | 'arrow';
  'hub.manage.logs.config.fields.channel': never;
  'hub.manage.logs.config.fields.role': never;
  'hub.manage.logs.reports.label': never;
  'hub.manage.logs.reports.description': never;
  'hub.manage.logs.modLogs.label': never;
  'hub.manage.logs.modLogs.description': never;
  'hub.manage.logs.joinLeaves.label': never;
  'hub.manage.logs.joinLeaves.description': never;
  'hub.manage.logs.appeals.label': never;
  'hub.manage.logs.appeals.description': never;
  'hub.manage.logs.networkAlerts.label': never;
  'hub.manage.logs.networkAlerts.description': never;
  'hub.manage.logs.messageModeration.label': never;
  'hub.manage.logs.messageModeration.description': never;
  'hub.manage.logs.messageDeletions.label': never;
  'hub.manage.logs.messageDeletions.description': never;
  'hub.manage.logs.messageEdits.label': never;
  'hub.manage.logs.messageEdits.description': never;
  'hub.transfer.invalidUser': 'emoji';
  'hub.transfer.selfTransfer': 'emoji';
  'hub.transfer.botUser': 'emoji';
  'hub.transfer.confirm': 'hub' | 'newOwner';
  'hub.transfer.cancelled': 'emoji';
  'hub.transfer.error': 'emoji';
  'hub.transfer.success': 'emoji' | 'hub' | 'newOwner';
  'hub.transfer.timeout': 'emoji';
  'hub.rules.noRules': 'emoji';
  'hub.rules.list': 'emoji' | 'rules';
  'hub.rules.maxRulesReached': 'emoji' | 'max';
  'hub.rules.ruleExists': 'emoji';
  'hub.rules.selectedRule': 'number';
  'hub.rules.modal.add.title': never;
  'hub.rules.modal.add.label': never;
  'hub.rules.modal.add.placeholder': never;
  'hub.rules.modal.edit.title': never;
  'hub.rules.modal.edit.label': never;
  'hub.rules.modal.edit.placeholder': never;
  'hub.rules.select.placeholder': never;
  'hub.rules.select.option.label': 'number';
  'hub.rules.buttons.add': never;
  'hub.rules.buttons.edit': never;
  'hub.rules.buttons.delete': never;
  'hub.rules.buttons.back': never;
  'hub.rules.success.add': 'emoji';
  'hub.rules.success.edit': 'emoji';
  'hub.rules.success.delete': 'emoji';
  'hub.rules.view.title': 'number';
  'hub.rules.view.select': never;
  'hub.welcome.set': 'emoji';
  'hub.welcome.removed': 'emoji';
  'hub.welcome.voterOnly': 'emoji';
  'hub.welcome.placeholder': 'user' | 'serverName' | 'hubName' | 'memberCount' | 'totalConnections';
  'report.modal.title': never;
  'report.modal.other.label': never;
  'report.modal.other.placeholder': never;
  'report.modal.bug.input1.label': never;
  'report.modal.bug.input1.placeholder': never;
  'report.modal.bug.input2.label': never;
  'report.modal.bug.input2.placeholder': never;
  'report.reasons.spam': never;
  'report.reasons.advertising': never;
  'report.reasons.nsfw': never;
  'report.reasons.harassment': never;
  'report.reasons.hate_speech': never;
  'report.reasons.scam': never;
  'report.reasons.illegal': never;
  'report.reasons.personal_info': never;
  'report.reasons.impersonation': never;
  'report.reasons.breaks_hub_rules': never;
  'report.reasons.trolling': never;
  'report.reasons.misinformation': never;
  'report.reasons.gore_violence': never;
  'report.reasons.raid_organizing': never;
  'report.reasons.underage': never;
  'report.dropdown.placeholder': never;
  'report.submitted': 'emoji' | 'support_command';
  'report.errors.noReasonSelected': 'emoji';
  'report.errors.hubNotFound': 'emoji';
  'report.description': never;
  'report.options.message.description': never;
  'report.contextMenu.name': never;
  'report.selectReason': 'emoji';
  'report.bug.title': never;
  'report.bug.affected': never;
  'report.bug.description': never;
  'language.set': 'lang';
  'errors.messageNotSentOrExpired': 'emoji';
  'errors.notYourAction': 'emoji';
  'errors.notMessageAuthor': 'emoji';
  'errors.commandError': 'emoji' | 'support_invite' | 'errorId';
  'errors.mustVote': never;
  'errors.inviteLinks': 'emoji';
  'errors.invalidLangCode': 'emoji';
  'errors.modalError': 'emoji';
  'errors.unknownServer': 'emoji';
  'errors.unknownNetworkMessage': 'emoji';
  'errors.userNotFound': 'emoji';
  'errors.blacklisted': 'emoji' | 'hub';
  'errors.userBlacklisted': 'emoji';
  'errors.serverBlacklisted': 'emoji';
  'errors.serverNotBlacklisted': 'emoji';
  'errors.userNotBlacklisted': 'emoji';
  'errors.missingPermissions': 'emoji' | 'permissions';
  'errors.botMissingPermissions': 'emoji' | 'permissions';
  'errors.unknown': 'emoji' | 'support_invite';
  'errors.notUsable': 'emoji';
  'errors.cooldown': 'emoji' | 'time';
  'errors.serverNameInappropriate': 'emoji';
  'errors.banned': 'emoji' | 'support_invite';
  'errors.commandLoadingError': never;
  'errors.errorLoadingHubs': never;
  'errors.errorShowingHubSelection': never;
  'errors.connectNotFound': never;
  'config.setInvite.success': 'emoji';
  'config.setInvite.removed': 'emoji';
  'config.setInvite.invalid': 'emoji';
  'config.setInvite.notFromServer': 'emoji';
  'badges.shown': 'emoji';
  'badges.hidden': 'emoji';
  'badges.command.description': never;
  'badges.command.options.show.name': never;
  'badges.command.options.show.description': never;
  'badges.list.developer': never;
  'badges.list.staff': never;
  'badges.list.translator': never;
  'badges.list.voter': never;
  'global.webhookNoLongerExists': 'emoji';
  'global.noReason': never;
  'global.noDesc': never;
  'global.version': 'version';
  'global.loading': 'emoji';
  'global.reportOptionMoved': 'emoji' | 'support_invite';
  'global.private': never;
  'global.public': never;
  'global.yes': never;
  'global.no': never;
  'global.cancelled': 'emoji';
  'global.buttons.openInbox': never;
  'global.buttons.modPanel': never;
  'global.buttons.joinServer': never;
  'global.buttons.disconnect': never;
  'global.buttons.reconnect': never;
  'global.buttons.editRule': never;
  'global.buttons.joinPopularHub': never;
  'global.buttons.createNewHub': never;
  'global.buttons.finishSetup': never;
  'global.buttons.findMoreHubs': never;
  'global.buttons.supportServer': never;
  'global.buttons.viewChannel': never;
  'global.buttons.hubDirectory': never;
  'global.buttons.learnMore': never;
  'global.buttons.connectToHub': never;
  'global.buttons.createYourHub': never;
  'global.buttons.cancelCall': never;
  'global.buttons.newCall': never;
  'global.buttons.userLeaderboard': never;
  'global.buttons.serverLeaderboard': never;
  'global.modals.editConnection.title': never;
  'global.modals.editConnection.channelName.label': never;
  'global.modals.editConnection.channelName.placeholder': never;
  'global.modals.editConnection.profanityFilter.label': never;
  'global.modals.editConnection.compact.label': never;
  'global.modals.hubCreation.title': never;
  'global.modals.hubCreation.name.label': never;
  'global.modals.hubCreation.name.placeholder': never;
  'global.modals.hubCreation.description.label': never;
  'global.modals.hubCreation.description.placeholder': never;
  'global.modals.messageInfo.title': never;
  'global.modals.editRule.title': never;
  'global.modals.editRule.content.label': never;
  'global.modals.editRule.content.placeholder': never;
  'global.messages.selectChannel': never;
  'global.messages.selectHub': never;
  'global.messages.noHubsAvailable': never;
  'global.messages.hubNotFound': never;
  'global.messages.channelNotFound': never;
  'global.messages.connectionNotFound': never;
  'global.messages.invalidSelection': never;
  'global.messages.operationCancelled': never;
  'global.messages.setupComplete': never;
  'global.messages.connectionEstablished': never;
  'global.messages.connectionRemoved': never;
  'global.messages.settingsUpdated': never;
  'global.messages.ruleAdded': never;
  'global.messages.ruleUpdated': never;
  'global.messages.ruleDeleted': never;
  'global.messages.noDataAvailable': never;
  'global.messages.loadingData': never;
  'global.messages.connected': never;
  'global.messages.disconnected': never;
  'global.messages.paused': never;
  'global.messages.publicHub': never;
  'global.messages.privateHub': never;
  'leaderboard.title': never;
  'leaderboard.description': never;
  'warn.description': never;
  'warn.options.user.description': never;
  'warn.options.hub.description': never;
  'warn.options.reason.description': never;
  'warn.errors.cannotWarnSelf': 'emoji';
  'warn.modal.title': never;
  'warn.modal.reason.label': never;
  'warn.modal.reason.placeholder': never;
  'warn.success': 'emoji' | 'name';
  'warn.dm.title': 'emoji';
  'warn.dm.description': 'hubName';
  'warn.log.title': 'emoji';
  'warn.log.description':
    | 'arrow'
    | 'user'
    | 'userId'
    | 'arrow'
    | 'moderator'
    | 'modId'
    | 'arrow'
    | 'reason';
  'warn.log.footer': 'moderator';
  'calls.connected.title': never;
  'calls.connected.description': never;
  'calls.connected.instructions': never;
  'calls.connected.serverInfo': 'serverName' | 'memberCount';
  'calls.connected.duration': 'duration';
  'calls.connected.messages': 'count';
  'calls.waiting.title': never;
  'calls.waiting.description': never;
  'calls.failed.title': never;
  'calls.failed.description': never;
  'calls.failed.reasons.alreadyInCall': never;
  'calls.failed.reasons.alreadyInQueue': never;
  'calls.failed.reasons.webhookFailed': never;
  'calls.failed.reasons.channelInvalid': never;
  'calls.cancelled.title': never;
  'calls.cancelled.description': never;
  'calls.cancelled.queueExit': never;
  'calls.ended.title': never;
  'calls.ended.description': never;
  'calls.ended.stats': 'duration' | 'messages' | 'serverName';
  'calls.ended.ratePrompt': never;
  'calls.skip.title': never;
  'calls.skip.description': never;
  'calls.skip.newConnected.title': never;
  'calls.skip.newConnected.description': never;
  'calls.skip.error': never;
  'calls.hangup.confirm': never;
  'calls.hangup.success': never;
  'calls.hangup.queueOnly': never;
  'calls.buttons.endCall': never;
  'calls.buttons.skipServer': never;
  'calls.buttons.skipAgain': never;
  'calls.buttons.cancelCall': never;
  'calls.buttons.newCall': never;
  'calls.buttons.exploreHubs': never;
  'calls.buttons.browseAllHubs': never;
  'calls.buttons.ratePositive': never;
  'calls.buttons.rateNegative': never;
  'calls.buttons.reportCall': never;
  'calls.hubs.promotion.title': never;
  'calls.hubs.promotion.description': never;
  'calls.hubs.benefits.title': never;
  'calls.hubs.benefits.description': never;
  'calls.hubs.benefits.list': never;
  'calls.hubs.main.title': never;
  'calls.hubs.main.description': never;
  'calls.system.callStart': 'emoji' | 'guidelines';
  'calls.rating.success': 'type' | 'count' | 'plural';
  'calls.rating.alreadyRated': 'emoji';
  'calls.rating.invalidButton': 'emoji';
  'calls.rating.noCallData': 'emoji';
  'calls.rating.noParticipants': 'emoji';
  'calls.report.prompt': 'emoji';
  'calls.report.invalidButton': 'emoji';
  'calls.leaderboard.title': never;
  'calls.leaderboard.description': never;
  'calls.leaderboard.noData': never;
  'calls.leaderboard.userTab': never;
  'calls.leaderboard.serverTab': never;
  'calls.errors.guildOnly': never;
  'commands.about.title': never;
  'commands.about.description': never;
  'commands.about.description_text': never;
  'commands.about.support_text': never;
  'commands.about.features.title': never;
  'commands.about.features.list': never;
  'commands.about.buttons.vote': never;
  'commands.about.buttons.invite': never;
  'commands.about.buttons.dashboard': never;
  'commands.about.buttons.support': never;
  'commands.about.buttons.credits': never;
  'commands.about.credits.title': never;
  'commands.about.credits.developers': never;
  'commands.about.credits.staff': never;
  'commands.about.credits.translators': never;
  'commands.about.credits.mentions': never;
  'commands.about.credits.mascot': never;
  'commands.about.credits.top_voter': never;
  'commands.about.credits.footer': 'version';
  'commands.about.sections.invite': never;
  'commands.about.sections.dashboard': never;
  'commands.about.sections.support': never;
  'commands.about.sections.credits': never;
  'commands.about.errors.serverOnly': never;
  'commands.help.description': never;
  'commands.help.options.command.description': never;
  'commands.help.options.category.description': never;
  'commands.help.errors.categoryNotFound': 'emoji';
  'commands.help.errors.commandNotFound': 'emoji' | 'command';
  'commands.help.errors.showingCategory': 'emoji';
  'commands.help.errors.showingCommand': 'emoji';
  'commands.help.errors.showingMenu': 'emoji';
  'commands.help.errors.showingSearch': 'emoji';
  'commands.setup.description': never;
  'commands.setup.errors.serverOnly': never;
  'commands.setup.errors.missingPermissions': 'supportInvite';
  'commands.setup.errors.setupError': never;
  'commands.setup.errors.completionError': never;
  'commands.setup.errors.channelNotSelected': never;
  'commands.setup.errors.invalidChannelType': never;
  'commands.setup.errors.missingChannelPermissions': 'channel';
  'commands.setup.errors.channelAlreadyConnected': 'hubName';
  'commands.setup.errors.channelNotFound': never;
  'commands.setup.errors.hubNotFound': never;
  'commands.setup.errors.commandLoadingError': never;
  'commands.setup.errors.interactionError': 'emoji';
  'commands.setup.errors.userMismatch': never;
  'commands.setup.errors.serverRequired': never;
  'commands.setup.errors.timeout': never;
  'commands.setup.errors.noAvailableHubs': never;
  'commands.setup.errors.hubCreationFailed': never;
  'commands.setup.errors.validationError': never;
  'commands.setup.welcome.title': never;
  'commands.setup.welcome.description': never;
  'commands.setup.channelSelection.title': never;
  'commands.setup.channelSelection.description': never;
  'commands.setup.channelSelection.placeholder': never;
  'commands.setup.channelSelection.tips.title': never;
  'commands.setup.channelSelection.tips.content': never;
  'commands.setup.hubChoice.title': never;
  'commands.setup.hubChoice.description': 'channel';
  'commands.setup.hubChoice.whatIsHub.title': never;
  'commands.setup.hubChoice.whatIsHub.description': never;
  'commands.setup.hubChoice.popularHubs.title': never;
  'commands.setup.hubChoice.popularHubs.description': never;
  'commands.setup.hubChoice.createHub.title': never;
  'commands.setup.hubChoice.createHub.description': never;
  'commands.setup.hubChoice.note': never;
  'commands.setup.hubSelection.title': never;
  'commands.setup.hubSelection.description': never;
  'commands.setup.hubSelection.placeholder': never;
  'commands.setup.hubSelection.tip': never;
  'commands.setup.hubCreation.modal.title': never;
  'commands.setup.hubCreation.modal.name.label': never;
  'commands.setup.hubCreation.modal.name.placeholder': never;
  'commands.setup.hubCreation.modal.description.label': never;
  'commands.setup.hubCreation.modal.description.placeholder': never;
  'commands.setup.nextSteps.created.title': never;
  'commands.setup.nextSteps.created.description': 'hubName';
  'commands.setup.nextSteps.created.inviteLink.title': never;
  'commands.setup.nextSteps.created.inviteLink.description': 'hubInviteCommand' | 'hubName';
  'commands.setup.nextSteps.created.shareHub.title': never;
  'commands.setup.nextSteps.created.shareHub.description': 'dot' | 'dot' | 'supportInvite';
  'commands.setup.nextSteps.created.configuration.title': never;
  'commands.setup.nextSteps.created.configuration.description':
    | 'hubRulesCommand'
    | 'hubLoggingCommand'
    | 'hubAntiSwearCommand'
    | 'hubSettingsCommand';
  'commands.setup.nextSteps.created.proTips.title': never;
  'commands.setup.nextSteps.created.proTips.description':
    | 'dot'
    | 'dot'
    | 'dot'
    | 'website'
    | 'hubVisibilityCommand'
    | 'dot'
    | 'supportInvite';
  'commands.setup.nextSteps.created.copyCommand': 'hubName';
  'commands.setup.nextSteps.joined.title': never;
  'commands.setup.nextSteps.joined.description': 'hubName';
  'commands.setup.nextSteps.joined.commands':
    | 'connectionEditCommand'
    | 'connectionListCommand'
    | 'website';
  'commands.setup.nextSteps.joined.help': 'supportInvite';
  'commands.setup.completion.title': never;
  'commands.setup.completion.description': 'channel';
  'commands.setup.buttons.supportServer': never;
  'commands.setup.buttons.documentation': never;
  'commands.setup.buttons.goBack': never;
  'commands.setup.buttons.finishSetup': never;
  'commands.setup.buttons.hubDirectory': never;
  'commands.setup.buttons.learnMore': never;
  'commands.setup.buttons.viewChannel': never;
  'commands.setup.buttons.joinPopularHub': never;
  'commands.setup.buttons.createNewHub': never;
  'commands.setup.buttons.copyInviteCommand': never;
  'commands.setup.buttons.findMoreHubs': never;
  'commands.setup.existingConnections.title': never;
  'commands.setup.existingConnections.description': 'connectionList';
  'commands.language.description': never;
  'commands.language.options.lang.description': never;
  'commands.badges.description': never;
  'commands.badges.options.show.name': never;
  'commands.badges.options.show.description': never;
  'commands.tutorial.description': never;
  'commands.tutorial.subcommands.start.description': never;
  'commands.tutorial.subcommands.start.options.tutorial.description': never;
  'commands.tutorial.subcommands.setup.description': never;
  'commands.rules.description': never;
  'commands.rules.options.hub.description': never;
  'commands.rules.hubRules.title': 'hubName';
  'commands.rules.hubRules.description': never;
  'commands.rules.botRules.title': never;
  'commands.rules.botRules.description': never;
  'tutorial.errors.notFound': 'emoji';
  'tutorial.errors.noSteps': 'emoji';
  'tutorial.errors.prerequisitesRequired': 'emoji';
  'tutorial.errors.noInProgress': 'emoji';
  'tutorial.completion.completed': 'emoji';
  'tutorial.completion.nextRecommendation': 'tutorialName';
  'tutorial.categories.newUser': never;
  'tutorial.categories.admin': never;
  'tutorial.categories.moderator': never;
  'tutorial.categories.all': never;
  'tutorial.list.title': never;
  'tutorial.list.noTutorials': never;
  'tutorial.list.description': never;
  'tutorial.progress.completed': never;
  'tutorial.progress.inProgress': never;
  'tutorial.progress.notStarted': never;
  'tutorial.buttons.start': never;
  'tutorial.buttons.resume': never;
  'tutorial.buttons.review': never;
  'tutorial.buttons.next': never;
  'tutorial.buttons.previous': never;
  'tutorial.buttons.skip': never;
  'tutorial.buttons.finish': never;
  'tutorial.about.description': never;
  'tutorial.about.title': never;
  'tutorial.about.description_text': never;
  'tutorial.about.features.title': never;
  'tutorial.about.features.list': never;
  'tutorial.about.sections.invite': never;
  'tutorial.about.sections.dashboard': never;
  'tutorial.about.sections.support': never;
  'tutorial.about.sections.credits': never;
  'tutorial.about.buttons.invite': never;
  'tutorial.about.buttons.dashboard': never;
  'tutorial.about.buttons.support': never;
  'tutorial.about.buttons.credits': never;
  'tutorial.about.buttons.vote': never;
  'tutorial.about.support_text': never;
  'tutorial.about.credits.title': never;
  'tutorial.about.credits.developers': never;
  'tutorial.about.credits.staff': 'website';
  'tutorial.about.credits.translators': never;
  'tutorial.about.credits.mentions': never;
  'tutorial.about.credits.mascot': 'emoji';
  'tutorial.about.credits.top_voter': 'vote_url' | 'emoji';
  'tutorial.about.credits.footer': 'version';
  'hubConfig.antiSwear.title': never;
  'hubConfig.antiSwear.description': never;
  'hubConfig.antiSwear.noRules': never;
  'hubConfig.antiSwear.selectRule': never;
  'hubConfig.antiSwear.placeholder': never;
  'hubConfig.antiSwear.validating': 'emoji';
  'hubConfig.antiSwear.buttons.addRule': never;
  'hubConfig.antiSwear.buttons.editRule': never;
  'hubConfig.antiSwear.buttons.deleteRule': never;
  'hubConfig.antiSwear.buttons.back': never;
  'hubConfig.antiSwear.modal.addRule': never;
  'hubConfig.antiSwear.modal.editRule': never;
  'hubConfig.antiSwear.modal.ruleName': never;
  'hubConfig.antiSwear.modal.words': never;
  'hubConfig.antiSwear.modal.wordsPlaceholder': never;
  'hubConfig.logging.title': never;
  'hubConfig.logging.description': never;
  'hubConfig.logging.placeholder': never;
  'hubConfig.logging.channelSelect': never;
  'hubConfig.logging.roleSelect': never;
  'hubConfig.logging.config.title': 'type';
  'hubConfig.logging.config.description': 'arrow' | 'arrow';
  'hubConfig.logging.config.fields.channel': never;
  'hubConfig.logging.config.fields.role': never;
  'hubConfig.logging.types.reports.label': never;
  'hubConfig.logging.types.reports.description': never;
  'hubConfig.logging.types.modLogs.label': never;
  'hubConfig.logging.types.modLogs.description': never;
  'hubConfig.logging.types.joinLeaves.label': never;
  'hubConfig.logging.types.joinLeaves.description': never;
  'hubConfig.logging.types.appeals.label': never;
  'hubConfig.logging.types.appeals.description': never;
  'hubConfig.logging.types.networkAlerts.label': never;
  'hubConfig.logging.types.networkAlerts.description': never;
  'hubConfig.logging.types.messageModeration.label': never;
  'hubConfig.logging.types.messageModeration.description': never;
  'hubConfig.rules.title': never;
  'hubConfig.rules.description': never;
  'hubConfig.rules.noRules': never;
  'hubConfig.rules.maxRulesReached': 'max';
  'hubConfig.rules.ruleExists': never;
  'hubConfig.rules.placeholder': never;
  'hubConfig.rules.modal.add.title': never;
  'hubConfig.rules.modal.add.label': never;
  'hubConfig.rules.modal.add.placeholder': never;
  'hubConfig.rules.modal.edit.title': never;
  'hubConfig.rules.modal.edit.label': never;
  'hubConfig.rules.modal.edit.placeholder': never;
  'hubConfig.rules.buttons.add': never;
  'hubConfig.rules.buttons.edit': never;
  'hubConfig.rules.buttons.delete': never;
  'hubConfig.rules.buttons.back': never;
  'hubConfig.rules.view.title': 'number';
  'hubConfig.rules.view.select': never;
  'hubConfig.appealCooldown.errors.invalidCooldown': never;
  'hubConfig.appealCooldown.errors.tooShort': never;
  'hubConfig.appealCooldown.errors.tooLong': never;
  'hubConfig.appealCooldown.success': 'emoji' | 'hours';
  'interactions.modals.warn.title': never;
  'interactions.modals.warn.reason.label': never;
  'interactions.modals.warn.reason.placeholder': never;
  'interactions.buttons.refresh': never;
  'interactions.buttons.cancel': never;
  'interactions.buttons.confirm': never;
  'interactions.buttons.back': never;
  'interactions.buttons.next': never;
  'interactions.buttons.finish': never;
  'interactions.buttons.save': never;
  'interactions.buttons.delete': never;
  'interactions.buttons.edit': never;
  'interactions.buttons.add': never;
  'interactions.buttons.remove': never;
  'interactions.buttons.view': never;
  'interactions.buttons.close': never;
  'interactions.placeholders.selectOption': never;
  'interactions.placeholders.selectChannel': never;
  'interactions.placeholders.selectRole': never;
  'interactions.placeholders.selectUser': never;
  'interactions.placeholders.selectServer': never;
  'interactions.placeholders.enterText': never;
  'interactions.placeholders.enterReason': never;
  'interactions.placeholders.enterDescription': never;
  'modPanel.buttons.serverBanned': never;
  'modPanel.buttons.banServer': never;
  'modPanel.modals.blacklistUser': never;
  'modPanel.modals.blacklistServer': never;
  'ui.titles.error': never;
  'ui.titles.warning': never;
  'ui.titles.success': never;
  'ui.titles.info': never;
  'ui.titles.confirmation': never;
  'ui.messages.loading': never;
  'ui.messages.processing': never;
  'ui.messages.pleaseWait': never;
  'ui.messages.tryAgain': never;
  'ui.messages.contactSupport': never;
  'ui.messages.operationCancelled': never;
  'ui.messages.operationCompleted': never;
  'ui.messages.noDataAvailable': never;
  'ui.messages.permissionDenied': never;
  'ui.messages.invalidInput': never;
  'ui.messages.timeout': never;
  'ui.messages.notFound': never;
  'ui.messages.alreadyExists': never;
  'ui.messages.unavailable': never;
  'deleteMsg.description': never;
  'deleteMsg.options.message.description': never;
  'deleteMsg.contextMenu.name': never;
  'deleteMsg.processing': 'emoji';
  'deleteMsg.alreadyDeleted': 'emoji';
  'editMsg.description': never;
  'editMsg.options.message.description': never;
  'editMsg.options.newContent.description': never;
  'editMsg.contextMenu.name': never;
  'editMsg.modal.title': never;
  'editMsg.modal.content.label': never;
  'editMsg.modal.content.placeholder': never;
  'editMsg.processing': 'emoji';
  'editMsg.alreadyEdited': 'emoji';
  'inbox.description': never;
  'inbox.title': never;
  'inbox.subtitle.new': never;
  'inbox.subtitle.older': never;
  'inbox.empty.title': never;
  'inbox.empty.description': 'emoji';
  'inbox.buttons.viewOlder': never;
  'inbox.buttons.previous': never;
  'inbox.buttons.next': never;
  'inbox.postedOn': 'date';
  'joinserver.description': never;
  'joinserver.options.servername.description': never;
  'joinserver.options.messageorserverid.description': never;
  'joinserver.errors.channelOnly': never;
  'joinserver.errors.missingTarget': never;
  'joinserver.success.inviteSent': 'emoji';
  'joinserver.request.title': never;
  'joinserver.request.description': 'serverName';
  'joinserver.request.broadcast': 'username' | 'guildName';
  'joinserver.buttons.accept': never;
  'joinserver.buttons.reject': never;
  'joinserver.response.sent': 'emoji';
  'joinserver.response.creating': 'emoji';
  'joinserver.response.dmSent': 'emoji';
  'joinserver.response.dmFailed': 'emoji';
  'joinserver.status.accepted': 'username';
  'joinserver.status.rejected': 'username';
  'messageInfo.description': never;
  'messageInfo.options.message.description': never;
  'messageInfo.contextMenu.name': never;
  'messageInfo.errors.profileFetch': never;
  'connect.description': never;
  'connect.options.channel.description': never;
  'connect.options.invite.description': never;
  'connect.errors.invalidIds': 'emoji';
  'connect.errors.channelNotFound': 'emoji';
  'disconnect.description': never;
  'ban.description': never;
  'ban.options.duration.description': never;
  'ban.options.reason.description': never;
  'ban.options.user.description': never;
  'ban.options.serverId.description': never;
  'ban.errors.bothSpecified': 'emoji';
  'ban.errors.noneSpecified': 'emoji';
  'unban.description': never;
  'unban.options.user.description': never;
  'unban.options.serverId.description': never;
  'unban.errors.bothSpecified': 'emoji';
  'unban.errors.noneSpecified': 'emoji';
  'unban.errors.invalidTarget': 'emoji';
  'unban.errors.banNotFound': 'emoji';
  'unban.errors.serverBanNotFound': 'emoji';
  'unban.errors.loadFailed': 'emoji';
  'achievements.description': never;
  'achievements.options.user.description': never;
  'achievements.options.view.description': never;
  'achievements.title': 'username';
  'achievements.progress': 'unlocked' | 'total';
  'achievements.errors.userNotFound': never;
  'achievement.settings.enabled': never;
  'achievement.settings.disabled': never;
  'profile.description': never;
  'profile.options.user.description': never;
  'profile.errors.userNotFound': never;
  'rank.description': never;
  'rank.options.user.description': never;
  'rank.errors.createFailed': never;
  'call.description': never;
  'call.errors.skipFailed': never;
  'call.errors.connectNotFound': 'emoji';
  'hangup.description': never;
  'hangup.callEnded': 'user';
  'hangup.errors.error': never;
  'hangup.errors.callFailed': never;
  'hangup.errors.guildOnly': 'emoji';
  'hangup.errors.connectNotFound': 'emoji';
  'skip.description': never;
  'skip.errors.error': never;
  'skip.errors.skipFailed': never;
  'voteCommand.description': never;
  'welcome.buttons.back': never;
  'welcome.calls.title': never;
  'welcome.calls.description': never;
  'welcome.calls.commands': 'callCommand' | 'skipCommand' | 'hangupCommand' | 'leaderboardCommand';
  'welcome.calls.examples.title': never;
  'welcome.calls.examples.content': never;
  'welcome.setup.title': never;
  'welcome.setup.description': never;
  'welcome.setup.instructions': 'setupCommand' | 'connectCommand';
  'welcome.setup.buttons.runSetup': never;
  'welcome.setup.errors.commandNotFound': 'emoji';
};

export type ErrorLocaleKeys = Extract<keyof TranslationKeys, `errors.${string}`>;
