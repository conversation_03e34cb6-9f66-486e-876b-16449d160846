/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Donation system constants
 */

/**
 * Ko-fi related constants
 */
export const KOFI_CONSTANTS = {
  // Ko-fi webhook verification
  VERIFICATION_TOKEN_ENV: 'KOFI_VERIFICATION_TOKEN',

  // Ko-fi URLs
  DONATION_URL: 'https://ko-fi.com/interchat',
  MANAGE_SUBSCRIPTION_URL: 'https://ko-fi.com/interchat',

  // Ko-fi membership tiers
  SUPPORTER_TIER_NAME: 'supporter',
  SUPPORTER_MONTHLY_PRICE: 3.0,

  // Ko-fi webhook data structure
  WEBHOOK_DATA_FIELD: 'data',
} as const;

/**
 * Premium pricing constants
 */
export const PREMIUM_PRICING = {
  // One-time donation pricing for media premium
  MONTHLY_MEDIA_PREMIUM: 3.0, // $3.00 for 1 month
  YEARLY_MEDIA_PREMIUM: 30.0, // $30.00 for 1 year

  // Subscription pricing
  SUPPORTER_TIER_MONTHLY: 3.0, // $3.00/month for Ko-fi Supporter

  // Donor perk thresholds
  DONOR_BADGE_MINIMUM: 1.0, // $1.00 minimum for donor badge
  PRIORITY_SUPPORT_MINIMUM: 5.0, // $5.00 minimum for priority support
  EARLY_ACCESS_MINIMUM: 10.0, // $10.00 minimum for early access
  CUSTOM_THEME_MINIMUM: 15.0, // $15.00 minimum for custom themes
  VIP_BADGE_MINIMUM: 25.0, // $25.00 minimum for VIP badge
  SUPPORTER_BADGE_MINIMUM: 50.0, // $50.00 minimum for supporter badge
} as const;

/**
 * Premium duration constants (in days)
 */
export const PREMIUM_DURATION = {
  MONTHLY: 30,
  YEARLY: 365,
  SUBSCRIPTION_GRACE_PERIOD: 5, // Extra days for subscription renewals
  SUBSCRIPTION_DURATION: 35, // 30 days + 5 day grace period
} as const;

/**
 * Cache configuration constants
 */
export const CACHE_CONFIG = {
  PREMIUM_STATUS_TTL: 5 * 60 * 1000, // 5 minutes
  DONATION_STATS_TTL: 15 * 60 * 1000, // 15 minutes
  DONOR_RANK_TTL: 60 * 60 * 1000, // 1 hour

  // Cache key prefixes
  PREMIUM_STATUS_PREFIX: 'premium:status',
  SUPPORTER_TIER_PREFIX: 'premium:supporter',
  DONATION_STATS_PREFIX: 'donation:stats',
  DONOR_RANK_PREFIX: 'donation:rank',
} as const;

/**
 * Donation processing constants
 */
export const DONATION_PROCESSING = {
  // Response time targets
  COMMAND_RESPONSE_TARGET: 1000, // 1 second for command responses
  WEBHOOK_PROCESSING_TARGET: 5000, // 5 seconds for webhook processing

  // Retry configuration
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second base delay

  // Validation limits
  MAX_DONATION_AMOUNT: 10000.0, // $10,000 maximum
  MIN_DONATION_AMOUNT: 0.01, // $0.01 minimum
  MAX_MESSAGE_LENGTH: 500, // 500 characters for donation messages
} as const;

/**
 * Currency constants
 */
export const CURRENCY_CONFIG = {
  BASE_CURRENCY: 'USD',
  SUPPORTED_CURRENCIES: ['USD', 'EUR', 'GBP', 'CAD', 'AUD', 'JPY'],
  DECIMAL_PLACES: 2,

  // Default exchange rates (fallback values)
  DEFAULT_RATES: {
    USD: 1.0,
    EUR: 0.85,
    GBP: 0.73,
    CAD: 1.25,
    AUD: 1.35,
    JPY: 110.0,
  },
};

/**
 * Discord integration constants
 */
export const DISCORD_CONFIG = {
  // Role IDs
  DONOR_ROLE_ID: process.env.DONOR_ROLE_ID || '1234567890123456789',

  // Channel IDs for announcements
  DONATION_ANNOUNCEMENT_CHANNEL: process.env.DONATION_ANNOUNCEMENT_CHANNEL,

  // Embed colors
  DONOR_EMBED_COLOR: '#FF5722',
  PREMIUM_EMBED_COLOR: '#9C27B0',
  SUCCESS_EMBED_COLOR: '#4CAF50',
  ERROR_EMBED_COLOR: '#F44336',

  // Emojis
  DONOR_EMOJI: '☕',
  VIP_EMOJI: '💎',
  SUPPORTER_EMOJI: '🌟',
  PREMIUM_EMOJI: '👑',
} as const;

/**
 * Logging constants
 */
export const LOGGING_CONFIG = {
  // Log prefixes
  DONATION_PREFIX: '[donation]',
  SUBSCRIPTION_PREFIX: '[subscription]',
  MEMBERSHIP_PREFIX: '[membership]',
  PREMIUM_PREFIX: '[premium]',
  KOFI_PREFIX: '[kofi]',

  // Performance logging thresholds
  SLOW_OPERATION_THRESHOLD: 1000, // 1 second
  VERY_SLOW_OPERATION_THRESHOLD: 5000, // 5 seconds
} as const;

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  INVALID_KOFI_TOKEN: 'Invalid Ko-fi verification token',
  MISSING_KOFI_DATA: 'Missing Ko-fi webhook data',
  INVALID_JSON_DATA: 'Invalid JSON in Ko-fi webhook data',
  DONATION_PROCESSING_FAILED: 'Failed to process donation',
  PREMIUM_CHECK_FAILED: 'Failed to check premium status',
  INSUFFICIENT_PERMISSIONS: 'Insufficient permissions for premium feature',
  FEATURE_NOT_AVAILABLE: 'Premium feature not available',
  INVALID_DONATION_AMOUNT: 'Invalid donation amount',
  CURRENCY_CONVERSION_FAILED: 'Failed to convert currency',
} as const;

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  DONATION_PROCESSED: 'Donation processed successfully',
  PREMIUM_GRANTED: 'Premium access granted',
  PERK_GRANTED: 'Donor perk granted',
  SUBSCRIPTION_ACTIVATED: 'Subscription activated',
  FEATURE_UNLOCKED: 'Premium feature unlocked',
} as const;
