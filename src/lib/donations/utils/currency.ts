/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { CURRENCY_CONFIG } from './constants.js';
import type { CurrencyRates } from '../types/DonationTypes.js';

/**
 * Currency conversion utilities for donation processing
 */

/**
 * Convert amount from source currency to USD
 * @param amount Amount in source currency
 * @param sourceCurrency Source currency code
 * @param rates Optional custom exchange rates
 * @returns Amount in USD
 */
export function convertToUSD(
  amount: number,
  sourceCurrency: string,
  rates?: CurrencyRates,
): number {
  // If already USD, return as-is
  if (sourceCurrency.toUpperCase() === CURRENCY_CONFIG.BASE_CURRENCY) {
    return amount;
  }

  // Use provided rates or fall back to default rates
  const exchangeRates = rates || CURRENCY_CONFIG.DEFAULT_RATES;
  const rate = exchangeRates[sourceCurrency.toUpperCase() as keyof typeof exchangeRates];

  if (!rate) {
    throw new Error(`Unsupported currency: ${sourceCurrency}`);
  }

  // Convert to USD and round to 2 decimal places
  const usdAmount = amount / rate;
  return Math.round(usdAmount * 100) / 100;
}

/**
 * Format currency amount for display
 * @param amount Amount to format
 * @param currency Currency code
 * @param locale Locale for formatting (default: 'en-US')
 * @returns Formatted currency string
 */
export function formatCurrency(
  amount: number,
  currency: string,
  locale: string = 'en-US',
): string {
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency.toUpperCase(),
      minimumFractionDigits: CURRENCY_CONFIG.DECIMAL_PLACES,
      maximumFractionDigits: CURRENCY_CONFIG.DECIMAL_PLACES,
    }).format(amount);
  }
  catch {
    // Fallback formatting if Intl.NumberFormat fails
    return `${currency.toUpperCase()} ${amount.toFixed(CURRENCY_CONFIG.DECIMAL_PLACES)}`;
  }
}

/**
 * Validate currency code
 * @param currency Currency code to validate
 * @returns True if currency is supported
 */
export function isValidCurrency(currency: string): boolean {
  return CURRENCY_CONFIG.SUPPORTED_CURRENCIES.includes(
    currency.toUpperCase(),
  );
}


/**
 * Validate donation amount
 * @param amount Amount to validate
 * @param currency Currency code
 * @returns Validation result
 */
export function validateDonationAmount(
  amount: number,
  currency: string,
): { valid: boolean; error?: string } {
  // Check if currency is supported
  if (!isValidCurrency(currency)) {
    return {
      valid: false,
      error: `Unsupported currency: ${currency}`,
    };
  }

  // Convert to USD for validation
  let usdAmount: number;
  try {
    usdAmount = convertToUSD(amount, currency);
  }
  catch (error) {
    return {
      valid: false,
      error: `Currency conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    };
  }

  // Check minimum amount
  if (usdAmount < CURRENCY_CONFIG.DECIMAL_PLACES) {
    return {
      valid: false,
      error: `Donation amount too small. Minimum: ${formatCurrency(CURRENCY_CONFIG.DECIMAL_PLACES, 'USD')}`,
    };
  }

  // Check maximum amount (anti-fraud protection)
  if (usdAmount > 10000) {
    return {
      valid: false,
      error: `Donation amount too large. Maximum: ${formatCurrency(10000, 'USD')}`,
    };
  }

  return { valid: true };
}

/**
 * Round amount to appropriate decimal places for currency
 * @param amount Amount to round
 * @param currency Currency code
 * @returns Rounded amount
 */
export function roundCurrencyAmount(amount: number, currency: string): number {
  // Special handling for currencies with no decimal places (like JPY)
  if (currency.toUpperCase() === 'JPY') {
    return Math.round(amount);
  }

  // Default to 2 decimal places
  return Math.round(amount * 100) / 100;
}

/**
 * Get currency symbol
 * @param currency Currency code
 * @returns Currency symbol
 */
export function getCurrencySymbol(currency: string): string {
  const symbols: Record<string, string> = {
    USD: '$',
    EUR: '€',
    GBP: '£',
    CAD: 'C$',
    AUD: 'A$',
    JPY: '¥',
  };

  return symbols[currency.toUpperCase()] || currency.toUpperCase();
}
