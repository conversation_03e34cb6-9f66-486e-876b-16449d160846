# InterChat Donation System Library

This library handles all donation-related functionality for InterChat, including Ko-fi integration, premium features, and donor management.

## Architecture

The donation system is organized into several key components:

### Core Components
- **DonationManager**: Main donation processing and management
- **PremiumService**: Ko-fi Supporter tier verification and premium feature access

### Services
- **KofiWebhookService**: Ko-fi webhook processing logic
- **DonorPerkService**: Donor perk management and assignment

### Schemas & Types
- **Ko-fi Schemas**: Webhook payload validation and transformation
- **Donation Types**: Internal type definitions for donations and premium features

### Utilities
- **Currency Utils**: Currency conversion and formatting
- **Validation Utils**: Donation validation helpers
- **Constants**: Donation-specific constants and configuration

## Features

### Ko-fi Integration
- Webhook endpoint processing
- Supporter tier detection ($3/month)
- Automatic premium status updates
- Donation tracking and statistics

### Premium Features
- Unlimited media sharing
- Hub name customization
- Priority support access
- Donor badges and perks

### Performance Requirements
- Sub-1-second command response times
- Sub-10-second call matching compatibility
- <PERSON>is caching for premium status (5-minute TTL)
- Graceful error handling and fallbacks

## Usage

```typescript
import { DonationManager, PremiumService } from '#src/lib/donations';

// Check premium status
const premiumService = new PremiumService(donationManager, cacheManager);
const hasSupporter = await premiumService.hasSupporterTier(userId);

// Process Ko-fi donation
const donationManager = new DonationManager();
await donationManager.processDonation(kofiPayload, discordUserId);
```

## Database Integration

The donation system integrates with the following database models:
- `User` - Donation statistics and premium status
- `Donation` - Ko-fi transaction records
- `DonorPerk` - Available donor perks
- `UserDonorPerk` - User-specific perk assignments

## Security

- Ko-fi webhook verification using `KOFI_VERIFICATION_TOKEN`
- Premium status caching with TTL to prevent abuse
- Graceful degradation for non-premium users
- Input validation for all donation data
