/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { type KofiPayload, convertToUSD, toDonationData } from '../schemas/kofi.js';
import { DonorPerk, UserDonorPerk, Donation } from '#src/generated/prisma/client/index.js';

import Scheduler from '#src/services/SchedulerService.js';
import UserDbService from '#src/services/UserDbService.js';
import Constants from '#utils/Constants.js';
import db from '#utils/Db.js';
import Logger from '#utils/Logger.js';
import { getOrdinalSuffix } from '#utils/Utils.js';
import { stripIndents } from 'common-tags';
import { ClusterManager } from 'discord-hybrid-sharding';
import {
  type APIGuildMember,
  type APIUser,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
  EmbedBuilder,
  REST,
  Routes,
  WebhookClient,
  userMention,
} from 'discord.js';

export class DonationManager {
  private scheduler: Scheduler;
  private readonly userDbManager = new UserDbService();
  private readonly rest = new REST({ version: '10' }).setToken(process.env.DISCORD_TOKEN as string);
  private clusterManager: ClusterManager | null = null;

  constructor(scheduler = new Scheduler()) {
    this.scheduler = scheduler;

    // Schedule cleanup of expired donor perks
    this.scheduler.addRecurringTask('cleanupExpiredDonorPerks', 60 * 60 * 1_000, async () => {
      const expiredPerks = await db.userDonorPerk.findMany({
        where: {
          expiresAt: { lt: new Date() },
          isActive: true,
        },
        include: { user: true, perk: true },
      });

      for (const expiredPerk of expiredPerks) {
        await this.removeExpiredDonorPerk(expiredPerk);
      }
    });

    // Schedule cleanup of expired media premium subscriptions
    this.scheduler.addRecurringTask('cleanupExpiredMediaPremium', 60 * 60 * 1_000, async () => {
      const expiredUsers = await db.user.findMany({
        where: {
          hasMediaPremium: true,
          mediaPremiumExpiresAt: { lt: new Date() },
        },
      });

      for (const user of expiredUsers) {
        await this.userDbManager.updateUser(user.id, {
          hasMediaPremium: false,
          mediaPremiumExpiresAt: null,
        });
        Logger.info(`[subscription] Expired media premium for user ${user.id}`);
      }
    });
  }

  /**
   * Process a Ko-fi donation or subscription webhook payload
   */
  async processDonation(payload: KofiPayload, discordUserId?: string): Promise<void> {
    const isSubscription = payload.type === 'Subscription' || payload.is_subscription_payment;
    const logType = isSubscription ? 'subscription' : 'donation';

    Logger.info(
      `[${logType}] Processing ${logType}: ${payload.amount} ${payload.currency} from ${payload.from_name}${payload.tier_name ? ` (${payload.tier_name} tier)` : ''}`,
    );

    try {
      // Convert payload to our internal format
      const donationData = toDonationData(payload);
      const amountUsd = convertToUSD(donationData.amount, donationData.currency);

      // If no Discord user ID provided, we can't link the donation/subscription
      if (!discordUserId) {
        Logger.warn(
          `[${logType}] No Discord user ID provided for ${logType} ${payload.kofi_transaction_id}`,
        );
        // Still store the donation for record keeping
        await this.storeDonation(donationData, null, amountUsd);
        return;
      }

      // Handle Ko-fi membership tiers (subscriptions)
      if (isSubscription && payload.tier_name) {
        await this.processMembershipTier(discordUserId, payload.tier_name, payload, amountUsd);
      }
      else {
        // Handle regular one-time donations
        await this.processRegularDonation(discordUserId, donationData, amountUsd);
      }

      Logger.info(`[${logType}] Successfully processed ${logType} ${payload.kofi_transaction_id}`);
    }
    catch (error) {
      Logger.error(`[${logType}] Failed to process ${logType} ${payload.kofi_transaction_id}`, error);
      throw error;
    }
  }

  /**
   * Process Ko-fi membership tier subscription
   */
  private async processMembershipTier(
    discordUserId: string,
    tierName: string,
    payload: KofiPayload,
    amountUsd: number,
  ): Promise<void> {
    Logger.info(`[membership] Processing ${tierName} tier for user ${discordUserId}`);

    // Handle 'Supporter' tier specifically for media premium
    if (tierName.toLowerCase() === 'supporter') {
      await this.handleSupporterTierSubscription(discordUserId, payload, amountUsd);
    }
    else {
      Logger.warn(`[membership] Unknown tier: ${tierName}, treating as regular donation`);
      // Fallback to regular donation processing for unknown tiers
      const donationData = toDonationData(payload);
      await this.processRegularDonation(discordUserId, donationData, amountUsd);
    }
  }

  /**
   * Handle 'Supporter' tier subscription for media premium
   */
  private async handleSupporterTierSubscription(
    discordUserId: string,
    payload: KofiPayload,
    amountUsd: number,
  ): Promise<void> {
    const donationData = toDonationData(payload);

    // Store the subscription as a donation record
    const donation = await this.storeDonation(donationData, discordUserId, amountUsd);

    // Update user donation stats
    await this.updateUserDonationStats(discordUserId, amountUsd);

    // Grant media premium for Supporter tier
    await this.grantMediaPremiumSubscription(discordUserId, payload.is_first_subscription_payment);

    // Process regular donor perks as well
    await this.processDonorPerks(discordUserId, amountUsd);

    // Send thank you DM for subscription
    await this.sendSubscriptionThankYouDM(discordUserId, donation, 'Supporter');

    // Announce subscription (if public)
    if (donationData.isPublic) {
      await this.announceSubscription(discordUserId, donation, 'Supporter');
    }

    // Mark donation as processed
    await db.donation.update({
      where: { id: donation.id },
      data: { processed: true, processedAt: new Date() },
    });
  }

  /**
   * Process regular one-time donation
   */
  private async processRegularDonation(
    discordUserId: string,
    donationData: ReturnType<typeof toDonationData>,
    amountUsd: number,
  ): Promise<void> {
    // Store the donation in database
    const donation = await this.storeDonation(donationData, discordUserId, amountUsd);

    // Update user donation stats
    await this.updateUserDonationStats(discordUserId, amountUsd);

    // Process donor perks (including one-time media premium)
    await this.processDonorPerks(discordUserId, amountUsd);

    // Send thank you DM
    await this.sendDonationThankYouDM(discordUserId, donation);

    // Announce donation (if public)
    if (donationData.isPublic) {
      await this.announceDonation(discordUserId, donation);
    }

    // Mark donation as processed
    await db.donation.update({
      where: { id: donation.id },
      data: { processed: true, processedAt: new Date() },
    });
  }

  /**
   * Store donation in database
   */
  private async storeDonation(
    donationData: ReturnType<typeof toDonationData>,
    userId: string | null,
    amountUsd: number,
  ) {
    const createData = {
      ...donationData,
      amountUsd,
      processed: false,
      ...(userId && { userId }),
    };

    return await db.donation.create({
      data: createData,
    });
  }

  /**
   * Update user's donation statistics
   */
  private async updateUserDonationStats(userId: string, amountUsd: number): Promise<void> {
    const user = await this.userDbManager.getUser(userId);
    const currentTotal = user?.totalDonated ?? 0;
    const currentCount = user?.donationCount ?? 0;

    await this.userDbManager.upsertUser(userId, {
      totalDonated: currentTotal + amountUsd,
      donationCount: currentCount + 1,
      lastDonatedAt: new Date(),
      isDonor: true,
    });
  }

  /**
   * Process donor perks based on donation amount
   */
  private async processDonorPerks(userId: string, amountUsd: number): Promise<void> {
    // Get user's total donation amount
    const user = await db.user.findUnique({ where: { id: userId } });
    const totalDonated = (user?.totalDonated ?? 0) + amountUsd;

    // Get available donor perks that user qualifies for
    const availablePerks = await db.donorPerk.findMany({
      where: {
        isActive: true,
        minimumDonation: { lte: totalDonated },
      },
    });

    // Grant new perks
    for (const perk of availablePerks) {
      const existingPerk = await db.userDonorPerk.findUnique({
        where: { userId_perkId: { userId, perkId: perk.id } },
      });

      if (!existingPerk) {
        await this.grantDonorPerk(userId, perk.id, perk.duration);
      }
    }

    // Add donor role in support server
    await this.addDonorRole(userId);

    // Handle media premium for qualifying donations
    await this.handleMediaPremium(userId, amountUsd);
  }

  /**
   * Grant a donor perk to a user
   */
  private async grantDonorPerk(
    userId: string,
    perkId: string,
    duration?: number | null,
  ): Promise<void> {
    const expiresAt = duration ? new Date(Date.now() + duration * 24 * 60 * 60 * 1000) : null;

    await db.userDonorPerk.create({
      data: {
        userId,
        perkId,
        expiresAt,
        isActive: true,
      },
    });

    Logger.info(`[donation] Granted perk ${perkId} to user ${userId}`);
  }

  /**
   * Remove expired donor perk
   */
  private async removeExpiredDonorPerk(
    expiredPerk: UserDonorPerk & { perk: DonorPerk },
  ): Promise<void> {
    await db.userDonorPerk.update({
      where: { id: expiredPerk.id },
      data: { isActive: false },
    });

    Logger.info(
      `[donation] Removed expired perk ${expiredPerk.perk.name} from user ${expiredPerk.userId}`,
    );
  }

  /**
   * Add donor role in support server
   */
  async addDonorRole(userId: string): Promise<void> {
    if (!Constants.DonorRoleId) {
      Logger.warn('[donation] DONOR_ROLE_ID not configured');
      return;
    }

    await this.modifyUserRole('add', { userId, roleId: Constants.DonorRoleId });
  }

  /**
   * Remove donor role from support server
   */
  async removeDonorRole(userId: string): Promise<void> {
    if (!Constants.DonorRoleId) return;
    await this.modifyUserRole('remove', { userId, roleId: Constants.DonorRoleId });
  }

  /**
   * Modify user role in support server
   */
  private async modifyUserRole(
    type: 'add' | 'remove',
    { userId, roleId }: { userId: string; roleId: string },
  ): Promise<void> {
    try {
      const userInGuild = (await this.rest
        .get(Routes.guildMember(Constants.SupportServerId, userId))
        .catch(() => null)) as APIGuildMember | null;

      if (type === 'remove' && !userInGuild?.roles.includes(roleId)) return;

      const method = type === 'add' ? 'put' : 'delete';
      await this.rest[method](Routes.guildMemberRole(Constants.SupportServerId, userId, roleId));
    }
    catch (error) {
      Logger.error(`[donation] Failed to ${type} role ${roleId} for user ${userId}`, error);
    }
  }

  /**
   * Send thank you DM to donor
   */
  private async sendDonationThankYouDM(userId: string, donation: Donation): Promise<void> {
    if (!this.clusterManager) return;

    const user = await db.user.findUnique({ where: { id: userId } });
    const donationCount = user?.donationCount ?? 1;
    const ordinalSuffix = getOrdinalSuffix(donationCount);

    const thankYouEmbed = new EmbedBuilder()
      .setTitle('Thank You for Your Donation! 💖')
      .setDescription(
        stripIndents`
          ☕ Thank you so much for supporting InterChat with your generous donation!

          🎉 This is your **${donationCount}${ordinalSuffix}** donation!
          💰 Amount: **$${donation.amountUsd.toFixed(2)} USD**

          **🎁 Donor Perks:**
          ${this.getDonorPerksText()}

          Your support helps us keep InterChat running and improving! ❤️
        `,
      )
      .setColor('#FF5722')
      .setFooter({
        text: 'InterChat - Donation Thank You',
        iconURL: 'https://i.imgur.com/NKKmav5.gif',
      });

    const donateButton = new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setStyle(ButtonStyle.Link)
        .setLabel('Donate Again')
        .setEmoji('☕')
        .setURL(Constants.Links.Donate),
    );

    // Send DM through cluster manager
    await this.clusterManager.broadcastEval(
      async (client, ctx) => {
        const discordUser = await client.users.fetch(ctx.userId).catch(() => null);
        if (!discordUser) return false;

        await discordUser
          .send({ embeds: [ctx.thankYouEmbed], components: [ctx.donateButton] })
          .catch(() => null);
        return true;
      },
      {
        context: {
          userId,
          thankYouEmbed: thankYouEmbed.toJSON(),
          donateButton: donateButton.toJSON(),
        },
        shard: 0,
      },
    );
  }

  /**
   * Announce donation in support server
   */
  private async announceDonation(userId: string, donation: Donation): Promise<void> {
    if (!process.env.DONATION_WEBHOOK_URL) return;

    const webhook = new WebhookClient({ url: process.env.DONATION_WEBHOOK_URL });
    const userMentionStr = userMention(userId);
    const username = await this.getUsername(userId);

    await webhook.send({
      content: `${userMentionStr} (**${username}**)`,
      embeds: [
        new EmbedBuilder()
          .setDescription(
            stripIndents`
            ☕ ${username} just made a donation! Thank you for supporting InterChat!

            💰 **Amount:** $${donation.amountUsd.toFixed(2)} USD
            ${donation.message ? `💬 **Message:** "${donation.message}"` : ''}

            -# 🎉 Every donation helps us keep InterChat free and improving!
            `,
          )
          .setColor('#FF5722'),
      ],
    });
  }

  /**
   * Get username for user
   */
  private async getUsername(userId: string): Promise<string> {
    const user = (await this.getAPIUser(userId)) ?? (await this.userDbManager.getUser(userId));
    return user && 'username' in user ? user.username : (user?.name ?? 'Unknown User');
  }

  /**
   * Get API user
   */
  private async getAPIUser(userId: string): Promise<APIUser | null> {
    const user = await this.rest.get(Routes.user(userId)).catch(() => null);
    return user as APIUser | null;
  }

  /**
   * Get donor perks text
   */
  private getDonorPerksText(): string {
    return stripIndents`
      - Special donor badge in profile & messages
      - Donor role in support server
      - Priority support
      - Early access to new features
      - Exclusive donor-only channels
      - Media Premium ($2.99+): Unlimited media sharing in calls
    `;
  }

  /**
   * Set cluster manager
   */
  public setClusterManager(clusterManager: ClusterManager): void {
    this.clusterManager = clusterManager;
  }

  /**
   * Get user's total donation amount
   */
  async getUserTotalDonated(userId: string): Promise<number> {
    const user = await this.userDbManager.getUser(userId);
    return user?.totalDonated ?? 0;
  }

  /**
   * Get user's donation count
   */
  async getUserDonationCount(userId: string): Promise<number> {
    const user = await this.userDbManager.getUser(userId);
    return user?.donationCount ?? 0;
  }

  /**
   * Check if user is a donor
   */
  async isUserDonor(userId: string): Promise<boolean> {
    const user = await this.userDbManager.getUser(userId);
    return user?.isDonor ?? false;
  }

  /**
   * Handle media premium for qualifying donations
   * $2.99+ grants 1 month
   */
  private async handleMediaPremium(userId: string, amountUsd: number): Promise<void> {
    const MONTHLY_PRICE = 2.99;

    let premiumDuration = 0; // in days

    if (amountUsd >= MONTHLY_PRICE) {
      // Grant 1 month of premium
      premiumDuration = 30;
    }
    else {
      // Donation too small for media premium
      return;
    }

    const user = await this.userDbManager.getUser(userId);
    const currentExpiry = user?.mediaPremiumExpiresAt;

    // Calculate new expiry date
    const baseDate = currentExpiry && currentExpiry > new Date()
      ? currentExpiry
      : new Date();

    const newExpiryDate = new Date(baseDate.getTime() + premiumDuration * 24 * 60 * 60 * 1000);

    // Update user's media premium status
    await this.userDbManager.updateUser(userId, {
      hasMediaPremium: true,
      mediaPremiumExpiresAt: newExpiryDate,
    });

    Logger.info(
      `[donation] Granted ${premiumDuration} days of media premium to user ${userId} (expires: ${newExpiryDate.toISOString()})`,
    );
  }

  /**
   * Grant media premium subscription for 'Supporter' tier
   * Supporter tier grants ongoing premium as long as subscription is active
   */
  private async grantMediaPremiumSubscription(
    userId: string,
    isFirstPayment?: boolean,
  ): Promise<void> {
    const user = await this.userDbManager.getUser(userId);
    const currentExpiry = user?.mediaPremiumExpiresAt;

    // For subscriptions, grant 35 days (30 days + 5 day grace period)
    // This ensures continuous coverage even if Ko-fi is slightly delayed
    const subscriptionDuration = 35; // days

    // Calculate new expiry date
    const baseDate = currentExpiry && currentExpiry > new Date()
      ? currentExpiry
      : new Date();

    const newExpiryDate = new Date(baseDate.getTime() + subscriptionDuration * 24 * 60 * 60 * 1000);

    // Update user's media premium status
    await this.userDbManager.updateUser(userId, {
      hasMediaPremium: true,
      mediaPremiumExpiresAt: newExpiryDate,
    });

    const logMessage = isFirstPayment
      ? `[subscription] Granted initial media premium subscription to user ${userId} (expires: ${newExpiryDate.toISOString()})`
      : `[subscription] Renewed media premium subscription for user ${userId} (expires: ${newExpiryDate.toISOString()})`;

    Logger.info(logMessage);
  }

  /**
   * Send thank you DM for subscription
   */
  private async sendSubscriptionThankYouDM(
    userId: string,
    donation: Donation,
    tierName: string,
  ): Promise<void> {
    if (!this.clusterManager) return;

    const user = await db.user.findUnique({ where: { id: userId } });
    const donationCount = user?.donationCount ?? 1;
    const ordinalSuffix = getOrdinalSuffix(donationCount);

    const thankYouEmbed = new EmbedBuilder()
      .setTitle(`Thank You for Your ${tierName} Subscription! 🌟`)
      .setDescription(
        stripIndents`
          ⭐ Thank you for subscribing to the **${tierName}** tier! You're amazing!

          🎉 This is your **${donationCount}${ordinalSuffix}** contribution!
          💰 Monthly Amount: **$${donation.amountUsd.toFixed(2)} USD**

          **🎁 ${tierName} Benefits:**
          - ⭐ **Unlimited Media Sharing** in calls (GIFs, images, stickers)
          - 🎖️ Special donor badge in profile & messages
          - 🏆 Donor role in support server
          - 🚀 Priority support & early access to features

          Your ongoing support keeps InterChat thriving! ❤️
        `,
      )
      .setColor('#FFD700')
      .setFooter({
        text: 'InterChat - Subscription Thank You',
        iconURL: 'https://i.imgur.com/NKKmav5.gif',
      });

    const manageButton = new ActionRowBuilder<ButtonBuilder>().addComponents(
      new ButtonBuilder()
        .setStyle(ButtonStyle.Link)
        .setLabel('Manage Subscription')
        .setEmoji('⚙️')
        .setURL(Constants.Links.Donate),
    );

    // Send DM through cluster manager
    await this.clusterManager.broadcastEval(
      async (client, ctx) => {
        const discordUser = await client.users.fetch(ctx.userId).catch(() => null);
        if (!discordUser) return false;

        await discordUser
          .send({ embeds: [ctx.thankYouEmbed], components: [ctx.manageButton] })
          .catch(() => null);
        return true;
      },
      {
        context: {
          userId,
          thankYouEmbed: thankYouEmbed.toJSON(),
          manageButton: manageButton.toJSON(),
        },
        shard: 0,
      },
    );
  }

  /**
   * Announce subscription in support server
   */
  private async announceSubscription(
    userId: string,
    donation: Donation,
    tierName: string,
  ): Promise<void> {
    if (!process.env.DONATION_WEBHOOK_URL) return;

    const webhook = new WebhookClient({ url: process.env.DONATION_WEBHOOK_URL });
    const userMentionStr = userMention(userId);
    const username = await this.getUsername(userId);

    await webhook.send({
      content: `${userMentionStr} (**${username}**)`,
      embeds: [
        new EmbedBuilder()
          .setDescription(
            stripIndents`
            ⭐ ${username} just subscribed to the **${tierName}** tier! Thank you for your ongoing support!

            💰 **Monthly Amount:** $${donation.amountUsd.toFixed(2)} USD
            🎁 **Benefits:** Unlimited media sharing + all donor perks
            ${donation.message ? `💬 **Message:** "${donation.message}"` : ''}

            -# 🌟 Subscriptions help us maintain and improve InterChat continuously!
            `,
          )
          .setColor('#FFD700'),
      ],
    });
  }

  /**
   * Check if user has active media premium
   */
  async hasMediaPremium(userId: string): Promise<boolean> {
    const user = await this.userDbManager.getUser(userId);
    if (!user?.hasMediaPremium) return false;

    // If no expiration date, it's permanent premium
    if (!user.mediaPremiumExpiresAt) return true;

    // Check if premium hasn't expired
    return user.mediaPremiumExpiresAt > new Date();
  }
}
