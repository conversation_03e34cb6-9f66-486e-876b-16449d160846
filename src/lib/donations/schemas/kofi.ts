/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import { z } from 'zod';
import type { DonationData } from '../types/DonationTypes.js';
import { convertToUSD } from '../utils/currency.js';

/**
 * Schema for validating Ko-fi webhook payloads
 * Based on Ko-fi webhook documentation and real payload examples
 */
export const kofiPayloadSchema = z.object({
  verification_token: z.string().uuid('Verification token must be a valid UUID'),
  message_id: z.string().uuid('Message ID must be a valid UUID'),
  timestamp: z.string().datetime('Timestamp must be a valid ISO-8601 datetime'),
  type: z.enum(['Donation', 'Subscription', 'Commission', 'Shop Order']),
  is_public: z.boolean(),
  from_name: z.string().min(1, 'From name cannot be empty'),
  message: z.string().nullable().optional(),
  amount: z.string().regex(/^\d+\.\d{2}$/, 'Amount must be in decimal format (e.g., "3.00")'),
  url: z.string().url('URL must be a valid URL').optional(),
  email: z.string().email('Email must be valid').optional(),
  currency: z.string().length(3, 'Currency must be a 3-letter code'),
  is_subscription_payment: z.boolean().optional().default(false),
  is_first_subscription_payment: z.boolean().optional().default(false),
  kofi_transaction_id: z.string().uuid('Transaction ID must be a valid UUID'),
  shop_items: z.array(z.any()).nullable().optional(),
  tier_name: z.string().nullable().optional(),
  shipping: z
    .object({
      full_name: z.string(),
      street_address: z.string(),
      city: z.string(),
      state_or_province: z.string(),
      postal_code: z.string(),
      country: z.string(),
      country_code: z.string(),
      telephone: z.string().optional(),
    })
    .nullable()
    .optional(),
});

export type KofiPayload = z.infer<typeof kofiPayloadSchema>;

/**
 * Schema for the Ko-fi webhook request body
 * Ko-fi sends data as form-encoded with a 'data' field containing JSON
 */
export const kofiWebhookSchema = z.object({
  data: z.string().transform((str, ctx) => {
    try {
      const parsed = JSON.parse(str);
      const result = kofiPayloadSchema.safeParse(parsed);
      if (!result.success) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Invalid Ko-fi payload structure',
          path: ['data'],
        });
        return z.NEVER;
      }
      return result.data;
    }
    catch {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Invalid JSON in data field',
        path: ['data'],
      });
      return z.NEVER;
    }
  }),
});

export type KofiWebhookPayload = z.infer<typeof kofiWebhookSchema>;

/**
 * Convert a Ko-fi payload to our internal donation format
 */
export const toDonationData = (payload: KofiPayload): DonationData => ({
  kofiTransactionId: payload.kofi_transaction_id,
  messageId: payload.message_id,
  amount: parseFloat(payload.amount),
  currency: payload.currency,
  fromName: payload.from_name,
  message: payload.message || null,
  email: payload.email || null,
  isPublic: payload.is_public,
  kofiTimestamp: new Date(payload.timestamp),
  kofiUrl: payload.url || null,
});

/**
 * Validate Ko-fi webhook verification token
 */
export const validateKofiWebhookToken = (providedToken: string, expectedToken: string): boolean => {
  if (!providedToken || !expectedToken) {
    return false;
  }
  return providedToken === expectedToken;
};

/**
 * Check if Ko-fi payload represents a subscription
 */
export const isKofiSubscription = (payload: KofiPayload): boolean =>
  payload.type === 'Subscription' || payload.is_subscription_payment === true;

/**
 * Check if Ko-fi payload is a first subscription payment
 */
export const isFirstSubscriptionPayment = (payload: KofiPayload): boolean =>
  payload.is_first_subscription_payment === true;

/**
 * Get Ko-fi membership tier name from payload
 */
export const getKofiTierName = (payload: KofiPayload): string | null => payload.tier_name || null;

/**
 * Extract Ko-fi transaction metadata
 */
export const extractKofiMetadata = (payload: KofiPayload) => ({
  transactionId: payload.kofi_transaction_id,
  messageId: payload.message_id,
  type: payload.type,
  isSubscription: isKofiSubscription(payload),
  isFirstPayment: isFirstSubscriptionPayment(payload),
  tierName: getKofiTierName(payload),
  timestamp: new Date(payload.timestamp),
  isPublic: payload.is_public,
});

// Re-export currency conversion function for backward compatibility
export { convertToUSD };
