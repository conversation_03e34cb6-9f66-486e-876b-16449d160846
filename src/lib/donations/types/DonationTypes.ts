/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import type { DonorPerk, UserDonorPerk, Donation } from '#src/generated/prisma/client/index.js';

/**
 * Internal donation data structure after Ko-fi payload transformation
 */
export interface DonationData {
  kofiTransactionId: string;
  messageId: string;
  amount: number;
  currency: string;
  fromName: string;
  message: string | null;
  email: string | null;
  isPublic: boolean;
  kofiTimestamp: Date;
  kofiUrl: string | null;
}

/**
 * Donation processing result
 */
export interface DonationProcessingResult {
  success: boolean;
  donation?: Donation;
  error?: string;
  premiumGranted?: boolean;
  perksGranted?: DonorPerk[];
}

/**
 * User donation statistics
 */
export interface UserDonationStats {
  totalDonated: number;
  donationCount: number;
  lastDonatedAt: Date | null;
  isDonor: boolean;
  rank?: number;
}

/**
 * Donor perk assignment result
 */
export interface DonorPerkAssignment {
  perk: DonorPerk;
  granted: boolean;
  expiresAt: Date | null;
  reason?: string;
}

/**
 * Donation announcement data
 */
export interface DonationAnnouncement {
  donorName: string;
  amount: number;
  currency: string;
  message?: string;
  isPublic: boolean;
  isFirstTime: boolean;
}

/**
 * Ko-fi membership tier information
 */
export interface MembershipTier {
  name: string;
  monthlyPrice: number;
  features: string[];
  duration: number; // in days
}

/**
 * Supported Ko-fi membership tiers
 */
export const MEMBERSHIP_TIERS: Record<string, MembershipTier> = {
  supporter: {
    name: 'Supporter',
    monthlyPrice: 3.0,
    features: ['unlimited_media_sharing', 'hub_name_customization', 'priority_support'],
    duration: 35, // 30 days + 5 day grace period
  },
} as const;

/**
 * Currency conversion rates (updated periodically)
 */
export interface CurrencyRates {
  [currency: string]: number; // Rate to USD
}

/**
 * Donation validation result
 */
export interface DonationValidationResult {
  valid: boolean;
  error?: string;
  warnings?: string[];
}

export type { DonorPerk, UserDonorPerk, Donation };
