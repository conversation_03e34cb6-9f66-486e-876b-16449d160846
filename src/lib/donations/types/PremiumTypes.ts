/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Premium feature types and interfaces for InterChat donation system
 */

/**
 * Available premium features
 */
export enum PremiumFeature {
  UNLIMITED_MEDIA_SHARING = 'unlimited_media_sharing',
  HUB_NAME_CUSTOMIZATION = 'hub_name_customization',
  PRIORITY_SUPPORT = 'priority_support',
  CUSTOM_PROFILE_THEME = 'custom_profile_theme',
  EARLY_ACCESS = 'early_access',
  DONOR_BADGE = 'donor_badge',
  VIP_DONOR_BADGE = 'vip_donor_badge',
  SUPPORTER_BADGE = 'supporter_badge',
}

/**
 * Premium tier levels
 */
export enum PremiumTier {
  NONE = 'none',
  DONOR = 'donor',
  SUPPORTER = 'supporter',
  VIP = 'vip',
}

/**
 * Premium status information
 */
export interface PremiumStatus {
  hasSupporter: boolean;
  canCustomizeHubName: boolean;
  hasMediaPremium: boolean;
  tier: PremiumTier;
  expiresAt: Date | null;
  features: PremiumFeature[];
}

/**
 * Premium feature access result
 */
export interface PremiumFeatureAccess {
  hasAccess: boolean;
  feature: PremiumFeature;
  tier: PremiumTier;
  reason?: string;
  upgradeUrl?: string;
}

/**
 * Premium subscription information
 */
export interface PremiumSubscription {
  isActive: boolean;
  tier: PremiumTier;
  startDate: Date;
  expiresAt: Date | null;
  autoRenew: boolean;
  kofiTierName?: string;
}

/**
 * Premium feature configuration
 */
export interface PremiumFeatureConfig {
  feature: PremiumFeature;
  requiredTier: PremiumTier;
  minimumDonation?: number;
  requiresActiveSubscription: boolean;
  description: string;
  upgradeMessage: string;
}

/**
 * Premium feature configurations
 */
export const PREMIUM_FEATURES: Record<PremiumFeature, PremiumFeatureConfig> = {
  [PremiumFeature.UNLIMITED_MEDIA_SHARING]: {
    feature: PremiumFeature.UNLIMITED_MEDIA_SHARING,
    requiredTier: PremiumTier.SUPPORTER,
    minimumDonation: 3.0,
    requiresActiveSubscription: true,
    description: 'Share unlimited media in calls',
    upgradeMessage: 'Upgrade to Ko-fi Supporter ($3/month) for unlimited media sharing!',
  },
  [PremiumFeature.HUB_NAME_CUSTOMIZATION]: {
    feature: PremiumFeature.HUB_NAME_CUSTOMIZATION,
    requiredTier: PremiumTier.SUPPORTER,
    minimumDonation: 3.0,
    requiresActiveSubscription: true,
    description: 'Customize your hub name',
    upgradeMessage: 'Hub name customization is available to Ko-fi Supporters ($3/month).',
  },
  [PremiumFeature.PRIORITY_SUPPORT]: {
    feature: PremiumFeature.PRIORITY_SUPPORT,
    requiredTier: PremiumTier.DONOR,
    minimumDonation: 5.0,
    requiresActiveSubscription: false,
    description: 'Priority support in the InterChat support server',
    upgradeMessage: 'Donate $5+ to unlock priority support!',
  },
  [PremiumFeature.CUSTOM_PROFILE_THEME]: {
    feature: PremiumFeature.CUSTOM_PROFILE_THEME,
    requiredTier: PremiumTier.DONOR,
    minimumDonation: 15.0,
    requiresActiveSubscription: false,
    description: 'Customize your profile appearance',
    upgradeMessage: 'Donate $15+ to unlock custom profile themes!',
  },
  [PremiumFeature.EARLY_ACCESS]: {
    feature: PremiumFeature.EARLY_ACCESS,
    requiredTier: PremiumTier.DONOR,
    minimumDonation: 10.0,
    requiresActiveSubscription: false,
    description: 'Early access to new InterChat features',
    upgradeMessage: 'Donate $10+ to get early access to new features!',
  },
  [PremiumFeature.DONOR_BADGE]: {
    feature: PremiumFeature.DONOR_BADGE,
    requiredTier: PremiumTier.DONOR,
    minimumDonation: 1.0,
    requiresActiveSubscription: false,
    description: 'Special donor badge displayed in profile',
    upgradeMessage: 'Make any donation to unlock the donor badge!',
  },
  [PremiumFeature.VIP_DONOR_BADGE]: {
    feature: PremiumFeature.VIP_DONOR_BADGE,
    requiredTier: PremiumTier.VIP,
    minimumDonation: 25.0,
    requiresActiveSubscription: false,
    description: 'Exclusive VIP donor badge for major contributors',
    upgradeMessage: 'Donate $25+ to unlock the VIP donor badge!',
  },
  [PremiumFeature.SUPPORTER_BADGE]: {
    feature: PremiumFeature.SUPPORTER_BADGE,
    requiredTier: PremiumTier.VIP,
    minimumDonation: 50.0,
    requiresActiveSubscription: false,
    description: 'Special supporter badge for top contributors',
    upgradeMessage: 'Donate $50+ to unlock the supporter badge!',
  },
} as const;

/**
 * Premium cache configuration
 */
export interface PremiumCacheConfig {
  ttl: number; // Time to live in milliseconds
  keyPrefix: string;
}

/**
 * Default premium cache configuration
 */
export const DEFAULT_PREMIUM_CACHE_CONFIG: PremiumCacheConfig = {
  ttl: 5 * 60 * 1000, // 5 minutes
  keyPrefix: 'premium',
} as const;
