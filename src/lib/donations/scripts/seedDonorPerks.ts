/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import db from '#utils/Db.js';
import Logger from '#utils/Logger.js';
import { PREMIUM_PRICING } from '../utils/constants.js';

/**
 * Seed initial donor perks into the database
 */
async function seedDonorPerks() {
  Logger.info('[seed] Starting donor perks seeding...');

  const donorPerks = [
    {
      name: '<PERSON><PERSON> Badge',
      description: 'Special donor badge displayed in profile and messages',
      type: 'BADGE' as const,
      minimumDonation: PREMIUM_PRICING.DONOR_BADGE_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        badgeEmoji: '☕',
        badgeText: 'Donor',
        badgeColor: '#FF5722',
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'Donor Role',
      description: 'Special donor role in the InterChat support server',
      type: 'ROLE' as const,
      minimumDonation: PREMIUM_PRICING.DONOR_BADGE_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        roleId: process.env.DONOR_ROLE_ID || '1234567890123456789',
        roleName: 'Donor',
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'Priority Support',
      description: 'Priority support in the InterChat support server',
      type: 'FEATURE' as const,
      minimumDonation: PREMIUM_PRICING.PRIORITY_SUPPORT_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        feature: 'priority_support',
        priority: 'high',
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'Early Access',
      description: 'Early access to new InterChat features and updates',
      type: 'FEATURE' as const,
      minimumDonation: PREMIUM_PRICING.EARLY_ACCESS_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        feature: 'early_access',
        channels: ['beta_features', 'early_announcements'],
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'VIP Donor Badge',
      description: 'Exclusive VIP donor badge for major contributors',
      type: 'BADGE' as const,
      minimumDonation: PREMIUM_PRICING.VIP_BADGE_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        badgeEmoji: '💎',
        badgeText: 'VIP Donor',
        badgeColor: '#9C27B0',
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'Supporter Badge',
      description: 'Special supporter badge for top contributors',
      type: 'BADGE' as const,
      minimumDonation: PREMIUM_PRICING.SUPPORTER_BADGE_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        badgeEmoji: '🌟',
        badgeText: 'Supporter',
        badgeColor: '#FFD700',
      }),
      duration: null, // Permanent
      isActive: true,
    },
    {
      name: 'Custom Profile Theme',
      description: 'Ability to customize profile appearance',
      type: 'COSMETIC' as const,
      minimumDonation: PREMIUM_PRICING.CUSTOM_THEME_MINIMUM,
      requiresActive: false,
      value: JSON.stringify({
        feature: 'custom_theme',
        options: ['colors', 'backgrounds', 'borders'],
      }),
      duration: null, // Permanent
      isActive: true,
    },
  ];

  try {
    for (const perk of donorPerks) {
      const existingPerk = await db.donorPerk.findUnique({
        where: { name: perk.name },
      });

      if (existingPerk) {
        Logger.info(`[seed] Donor perk "${perk.name}" already exists, skipping...`);
        continue;
      }

      await db.donorPerk.create({
        data: perk,
      });

      Logger.info(`[seed] Created donor perk: ${perk.name}`);
    }

    Logger.info('[seed] Donor perks seeding completed successfully!');
  }
  catch (error) {
    Logger.error('[seed] Failed to seed donor perks:', error);
    throw error;
  }
}

/**
 * Main seeding function
 */
async function main() {
  try {
    await seedDonorPerks();
    Logger.info('[seed] All seeding completed successfully!');
  }
  catch (error) {
    Logger.error('[seed] Seeding failed:', error);
    process.exit(1);
  }
  finally {
    await db.$disconnect();
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { seedDonorPerks };
