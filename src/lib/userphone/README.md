# InterChat Calling Library

A high-performance, modular calling system for InterChat with sub-10-second matching times and sub-1-second command responses.

## Architecture Overview

```
src/lib/calling/
├── core/                    # Core interfaces and types
│   ├── types.ts            # Type definitions
│   ├── interfaces.ts       # Service interfaces
│   └── events.ts           # Event system
├── services/               # Business logic services
│   ├── MatchingEngine.ts   # Real-time matching algorithm
│   ├── CallManager.ts      # Call lifecycle management
│   ├── QueueManager.ts     # Queue operations
│   └── NotificationService.ts # Real-time notifications
├── infrastructure/         # Infrastructure concerns
│   ├── cache/             # Caching layer
│   │   ├── WebhookCache.ts
│   │   ├── CallCache.ts
│   │   └── QueueCache.ts
│   ├── database/          # Database operations
│   │   ├── CallRepository.ts
│   │   └── queries/
│   └── metrics/           # Performance monitoring
│       └── CallMetrics.ts
├── commands/              # Command handlers
│   ├── CallCommand.ts
│   ├── HangupCommand.ts
│   └── SkipCommand.ts
└── CallingLibrary.ts      # Main library facade
```

## Key Design Principles

1. **Separation of Concerns**: Each module has a single responsibility
2. **Dependency Injection**: Services are injected, not instantiated
3. **Interface-based Design**: All services implement interfaces
4. **Performance First**: Optimized data structures and caching
5. **Event-driven Architecture**: Loose coupling through events
6. **Testability**: All components are easily testable

## Performance Targets

- **Command Response Time**: <500ms (optimized for user retention)
- **Matching Time**: <10 seconds for 90% of requests
- **Queue Processing**: 100+ matches/second
- **Memory Usage**: Optimized data structures
- **Database Queries**: Minimal round trips

## Usage

```typescript
import { DistributedCallingLibrary } from '#src/lib/userphone/DistributedCallingLibrary.js';

// The library is automatically initialized in BaseClient
const distributedCallingLibrary = client.getDistributedCallingLibrary();

// Use the library
const result = await distributedCallingLibrary.initiateCall(channel, userId);
const hangupResult = await distributedCallingLibrary.hangupCall(channelId);
const skipResult = await distributedCallingLibrary.skipCall(channelId, userId);
```

## Architecture

The DistributedCallingLibrary is fully integrated into the InterChat system and provides:

- **Cross-cluster compatibility**: Handles Discord bot sharding across multiple clusters
- **Real-time matching**: Sub-10-second matching with optimized algorithms
- **Performance optimization**: Sub-500ms command response times
- **Simplified notifications**: Only "Looking for a Match..." messages to reduce spam
- **Distributed state management**: Consistent state across all bot instances
