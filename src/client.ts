/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

import '#src/instrument.js';
import InterChatClient from '#src/core/BaseClient.js';
import Logger from '#utils/Logger.js';
import 'dotenv/config';

const client = new InterChatClient();

client.on('debug', (debug) => Logger.debug(debug));
client.rest.on('restDebug', (debug) => Logger.debug(debug));
client.rest.on('rateLimited', (data) => Logger.warn('Rate limited: %O', data));

process.on('uncaughtException', Logger.error);

client.start();
