# Premium Hub Name Customization - Testing Guide

## Overview
This document outlines the testing procedures for the premium hub name customization feature.

## Features Implemented

### 1. Database Schema Updates ✅
- Added direct hub name editing (removed customDisplayName approach)
- Updated PostgreSQL, MongoDB, and dashboard schemas
- Created migration scripts

### 2. Premium Status Verification ✅
- `PremiumService` class for Ko-fi Supporter tier verification
- Caching with 5-minute TTL
- Integration with existing `DonationManager`

### 3. Discord Commands ✅
- `/hub customize-name` - Main subcommand
- `/rename` - Ergonomic standalone command with `rn` alias
- Premium verification and ownership checks
- Unique name validation
- Sub-1-second response time target

### 4. Backend API ✅
- `PATCH /api/hubs/[hubId]/customize-name` endpoint
- Premium status verification
- Rate limiting integration
- Unique name validation
- Proper error handling

### 5. Dashboard Integration ✅
- `HubNameCustomization` component
- Premium badge indicators
- Upgrade prompts for non-premium users
- Dark theme consistency

### 6. Premium UI Components ✅
- `PremiumBadge` component with variants
- `UpgradePrompt` modal and inline components
- `PremiumIndicator` for consistent styling

## Testing Checklist

### Discord Commands
- [ ] Test `/hub customize-name` with premium user
- [ ] Test `/hub customize-name` with non-premium user
- [ ] Test `/rename` command with premium user
- [ ] Test `/rename` command with non-premium user
- [ ] Verify ownership checks work correctly
- [ ] Test unique name validation
- [ ] Test premium name validation (special characters, length)
- [ ] Verify response times are under 1 second

### Dashboard
- [ ] Test hub name editing for premium users
- [ ] Test upgrade prompt for non-premium users
- [ ] Verify premium badges display correctly
- [ ] Test form validation and error handling
- [ ] Verify dark theme consistency

### API Endpoints
- [ ] Test premium status verification
- [ ] Test rate limiting
- [ ] Test unique name validation
- [ ] Test ownership verification
- [ ] Test error responses

### Integration
- [ ] Verify Ko-fi webhook integration
- [ ] Test premium status caching
- [ ] Verify database updates work correctly
- [ ] Test cross-platform consistency (Discord + Dashboard)

## Test Cases

### Premium User Tests
1. **Valid Name Change**
   - Input: "🎮 Gaming Hub Elite"
   - Expected: Success, hub renamed
   
2. **Special Characters**
   - Input: "Hub-Name_2024 (Official)!"
   - Expected: Success, premium validation allows special chars
   
3. **Long Name**
   - Input: 45-character name with emojis
   - Expected: Success, premium allows up to 50 chars

### Non-Premium User Tests
1. **Attempt Name Change**
   - Expected: Premium required message with upgrade prompt
   
2. **Dashboard Access**
   - Expected: Upgrade prompt component displayed

### Edge Cases
1. **Duplicate Name**
   - Input: Existing hub name
   - Expected: "Name already taken" error
   
2. **Invalid Characters**
   - Input: Name with unsupported characters
   - Expected: Validation error
   
3. **Empty Name**
   - Input: Empty or whitespace-only
   - Expected: Validation error

## Performance Requirements
- Discord commands: < 1 second response time
- Dashboard operations: < 2 seconds
- Premium status checks: Cached for 5 minutes
- API rate limiting: Applied per endpoint configuration

## Rollback Plan
If issues are discovered:
1. Disable new commands via feature flag
2. Revert database migrations if needed
3. Remove dashboard components
4. Monitor for any data inconsistencies

## Success Criteria
- [ ] All test cases pass
- [ ] Performance requirements met
- [ ] No breaking changes to existing functionality
- [ ] Premium verification works correctly
- [ ] UI/UX is consistent with InterChat design system
