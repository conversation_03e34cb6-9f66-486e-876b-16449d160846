<style>
/* Elegant Top.gg description - Modern, rounded design with proper image display */
.interchat-elegant {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    line-height: 1.6 !important;
    color: #ffffff !important;
    margin: 0 !important;
    padding: 0 !important;
}

.hero-section {
    text-align: center !important;
    padding: 3rem 2rem !important;
    background: linear-gradient(135deg, #2c2f36 0%, #1e2124 100%) !important;
    border-radius: 20px !important;
    margin-bottom: 2.5rem !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

.hero-title {
    font-size: 2.8em !important;
    font-weight: 800 !important;
    margin-bottom: 0.8rem !important;
    color: #ffffff !important;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.hero-subtitle {
    font-size: 1.3em !important;
    color: #b9bbbe !important;
    margin-bottom: 2.5rem !important;
    font-weight: 400 !important;
    max-width: 600px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.stats-row {
    display: flex !important;
    justify-content: center !important;
    gap: 2rem !important;
    flex-wrap: wrap !important;
    margin: 2rem 0 !important;
}

.stat-item {
    text-align: center !important;
    background: rgba(255, 255, 255, 0.08) !important;
    border: 1px solid rgba(145, 114, 216, 0.2) !important;
    border-radius: 16px !important;
    padding: 1.5rem 1.2rem !important;
    min-width: 120px !important;
    backdrop-filter: blur(10px) !important;
    transition: all 0.3s ease !important;
}

.stat-item:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(145, 114, 216, 0.2) !important;
    border-color: rgba(145, 114, 216, 0.4) !important;
}

.stat-number {
    font-size: 2em !important;
    font-weight: 700 !important;
    color: #9172D8 !important;
    display: block !important;
    margin-bottom: 0.3rem !important;
}

.stat-label {
    font-size: 0.95em !important;
    color: #b9bbbe !important;
    font-weight: 500 !important;
}

.problem-solution {
    background: linear-gradient(135deg, #36393f 0%, #2f3136 100%) !important;
    border-left: 4px solid #9172D8 !important;
    padding: 2rem !important;
    margin: 2.5rem 0 !important;
    border-radius: 16px !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2) !important;
}

.problem-solution h2 {
    color: #ffffff !important;
    font-size: 1.5em !important;
    margin-bottom: 1rem !important;
    font-weight: 600 !important;
}

.problem-solution p {
    color: #dcddde !important;
    margin-bottom: 1.2rem !important;
    font-size: 1.05em !important;
    line-height: 1.7 !important;
}

.features-showcase {
    margin: 3.5rem 0 !important;
}

.showcase-title {
    text-align: center !important;
    font-size: 2em !important;
    color: #ffffff !important;
    margin-bottom: 2.5rem !important;
    font-weight: 600 !important;
}

.slideshow-container {
    position: relative !important;
    max-width: 800px !important;
    margin: 2.5rem auto !important;
    background: linear-gradient(135deg, #2f3136 0%, #36393f 100%) !important;
    border-radius: 20px !important;
    overflow: hidden !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(145, 114, 216, 0.2) !important;
}

/* Hide radio buttons */
.slide-radio {
    display: none !important;
}

.slides-wrapper {
    position: relative !important;
    overflow: hidden !important;
    height: 700px !important;
}

.slides-container {
    display: flex !important;
    width: 300% !important;
    height: 100% !important;
    transition: transform 0.6s ease !important;
}

.slide {
    width: 33.333% !important;
    flex-shrink: 0 !important;
    display: flex !important;
    flex-direction: column !important;
    height: 100% !important;
}

.slide-image {
    width: 100% !important;
    height: 500px !important;
    object-fit: contain !important;
    object-position: center !important;
    background: #23272a !important;
    padding: 2rem !important;
    border-bottom: 1px solid rgba(145, 114, 216, 0.1) !important;
}

.slide-content {
    padding: 2rem !important;
    text-align: center !important;
    flex-grow: 1 !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    background: linear-gradient(135deg, #36393f 0%, #2f3136 100%) !important;
}

.slide-title {
    color: #ffffff !important;
    font-size: 1.4em !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
}

.slide-description {
    color: #b9bbbe !important;
    font-size: 1.05em !important;
    line-height: 1.6 !important;
}

/* Navigation labels styled as buttons */
.nav-label {
    position: absolute !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: rgba(145, 114, 216, 0.8) !important;
    color: white !important;
    width: 50px !important;
    height: 50px !important;
    border-radius: 50% !important;
    font-size: 1.4em !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    z-index: 10 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3) !important;
    user-select: none !important;
    font-weight: bold !important;
}

.nav-label:hover {
    background: #9172D8 !important;
    transform: translateY(-50%) scale(1.1) !important;
    box-shadow: 0 6px 20px rgba(145, 114, 216, 0.4) !important;
}

.nav-prev {
    left: 20px !important;
}

.nav-next {
    right: 20px !important;
}

/* Slide indicators */
.slide-indicators {
    display: flex !important;
    justify-content: center !important;
    gap: 1rem !important;
    padding: 1.5rem !important;
    background: rgba(0, 0, 0, 0.1) !important;
}

.indicator-label {
    width: 14px !important;
    height: 14px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.3) !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    display: block !important;
}

.indicator-label:hover {
    background: rgba(145, 114, 216, 0.6) !important;
    transform: scale(1.1) !important;
}

/* CSS-only slideshow logic */
#slide1:checked ~ .slides-wrapper .slides-container {
    transform: translateX(0%) !important;
}

#slide2:checked ~ .slides-wrapper .slides-container {
    transform: translateX(-33.333%) !important;
}

#slide3:checked ~ .slides-wrapper .slides-container {
    transform: translateX(-66.666%) !important;
}

/* Active indicator styling */
#slide1:checked ~ .slide-indicators .indicator1,
#slide2:checked ~ .slide-indicators .indicator2,
#slide3:checked ~ .slide-indicators .indicator3 {
    background: #9172D8 !important;
    transform: scale(1.3) !important;
    box-shadow: 0 2px 8px rgba(145, 114, 216, 0.4) !important;
}

/* Remove navigation buttons - keep it simple with just indicators and auto-advance */

/* Auto-advance animation */
@keyframes slideshow {
    0%, 30% { transform: translateX(0%); }
    33.333%, 63.333% { transform: translateX(-33.333%); }
    66.666%, 96.666% { transform: translateX(-66.666%); }
    100% { transform: translateX(0%); }
}

.slides-container {
    animation: slideshow 15s infinite !important;
}

/* Pause animation on hover */
.slideshow-container:hover .slides-container {
    animation-play-state: paused !important;
}

.quick-features {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)) !important;
    gap: 2rem !important;
    margin: 3rem 0 !important;
}

.quick-feature {
    background: linear-gradient(135deg, #36393f 0%, #2f3136 100%) !important;
    border: 1px solid rgba(145, 114, 216, 0.15) !important;
    border-radius: 16px !important;
    padding: 2rem !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
}

.quick-feature:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2) !important;
    border-color: rgba(145, 114, 216, 0.25) !important;
}

.quick-feature-icon {
    font-size: 2.2em !important;
    margin-bottom: 1rem !important;
    display: block !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
}

.quick-feature h3 {
    color: #ffffff !important;
    font-size: 1.2em !important;
    margin-bottom: 0.8rem !important;
    font-weight: 600 !important;
}

.quick-feature p {
    color: #b9bbbe !important;
    font-size: 0.95em !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

.setup-section {
    background: linear-gradient(135deg, #36393f 0%, #2f3136 100%) !important;
    border: 1px solid rgba(145, 114, 216, 0.2) !important;
    border-radius: 20px !important;
    padding: 2.5rem !important;
    margin: 3rem 0 !important;
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15) !important;
}

.setup-title {
    color: #ffffff !important;
    font-size: 1.6em !important;
    margin-bottom: 2rem !important;
    text-align: center !important;
    font-weight: 600 !important;
}

.setup-steps {
    list-style: none !important;
    padding: 0 !important;
    counter-reset: step !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

.setup-step {
    counter-increment: step !important;
    position: relative !important;
    padding: 1.2rem 1.2rem 1.2rem 4rem !important;
    margin: 1.2rem 0 !important;
    background: rgba(255, 255, 255, 0.05) !important;
    border-radius: 12px !important;
    color: #dcddde !important;
    font-size: 1.05em !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(145, 114, 216, 0.1) !important;
}

.setup-step:hover {
    background: rgba(255, 255, 255, 0.08) !important;
    transform: translateX(8px) !important;
    border-color: rgba(145, 114, 216, 0.2) !important;
}

.setup-step::before {
    content: counter(step) !important;
    position: absolute !important;
    left: 1.2rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: linear-gradient(135deg, #9172D8, #a084e0) !important;
    color: white !important;
    width: 2.2rem !important;
    height: 2.2rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-weight: 600 !important;
    font-size: 0.95em !important;
    box-shadow: 0 2px 8px rgba(145, 114, 216, 0.3) !important;
}

.testimonial {
    background: linear-gradient(135deg, #2f3136 0%, #36393f 100%) !important;
    border-left: 4px solid #57F287 !important;
    padding: 2rem !important;
    margin: 3rem 0 !important;
    border-radius: 16px !important;
    font-style: italic !important;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

.testimonial-text {
    color: #dcddde !important;
    font-size: 1.15em !important;
    margin-bottom: 1rem !important;
    line-height: 1.6 !important;
}

.testimonial-author {
    color: #b9bbbe !important;
    font-size: 0.95em !important;
    text-align: right !important;
    font-weight: 500 !important;
}

.cta-section {
    text-align: center !important;
    background: linear-gradient(135deg, #2f3136 0%, #36393f 100%) !important;
    border: 2px solid rgba(145, 114, 216, 0.2) !important;
    border-radius: 24px !important;
    padding: 3rem 2rem !important;
    margin: 3rem 0 !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
}

.cta-title {
    color: #ffffff !important;
    font-size: 1.8em !important;
    margin-bottom: 1rem !important;
    font-weight: 600 !important;
}

.cta-subtitle {
    color: #b9bbbe !important;
    margin-bottom: 2.5rem !important;
    font-size: 1.05em !important;
    max-width: 500px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.cta-buttons {
    display: flex !important;
    justify-content: center !important;
    gap: 1.5rem !important;
    flex-wrap: wrap !important;
    margin: 2rem 0 !important;
}

.btn-primary {
    background: linear-gradient(135deg, #9172D8, #a084e0) !important;
    color: white !important;
    padding: 1rem 2.5rem !important;
    border-radius: 12px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-size: 1.1em !important;
    transition: all 0.3s ease !important;
    border: none !important;
    box-shadow: 0 4px 16px rgba(145, 114, 216, 0.3) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #a084e0, #9172D8) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 24px rgba(145, 114, 216, 0.4) !important;
    color: white !important;
}

.btn-secondary {
    background: transparent !important;
    color: #9172D8 !important;
    border: 2px solid #9172D8 !important;
    padding: 0.8rem 2.3rem !important;
    border-radius: 12px !important;
    text-decoration: none !important;
    font-weight: 600 !important;
    font-size: 1.05em !important;
    transition: all 0.3s ease !important;
}

.btn-secondary:hover {
    background: rgba(145, 114, 216, 0.1) !important;
    color: #a084e0 !important;
    border-color: #a084e0 !important;
    transform: translateY(-2px) !important;
}

.cta-footer {
    margin-top: 2rem !important;
    color: #b9bbbe !important;
    font-size: 0.95em !important;
    line-height: 1.5 !important;
}

@media (max-width: 768px) {
    .stats-row, .cta-buttons {
        flex-direction: column !important;
        align-items: center !important;
    }

    .quick-features {
        grid-template-columns: 1fr !important;
    }

    .hero-title {
        font-size: 2.2em !important;
    }

    .hero-section {
        padding: 2rem 1.5rem !important;
    }

    .slides-wrapper {
        height: 600px !important;
    }

    .slide-image {
        height: 400px !important;
        padding: 1rem !important;
    }

    .slideshow-container {
        margin: 2rem 1rem !important;
    }
}
</style>

<div class="interchat-elegant">
    <!-- Hero Section -->
    <div class="hero-section">
        <h1 class="hero-title">🌐 InterChat</h1>
        <p class="hero-subtitle">Phone Calls & Community Hubs • Cross-Server Communication • Connect Discord servers instantly</p>

        <div class="stats-row">
            <div class="stat-item">
                <span class="stat-number">10K+</span>
                <span class="stat-label">Servers</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">500+</span>
                <span class="stat-label">Phone Daily Calls</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">50+</span>
                <span class="stat-label">Active Hubs</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">15K+</span>
                <span class="stat-label">Messages</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">99%</span>
                <span class="stat-label">Uptime</span>
            </div>
        </div>
    </div>

    <!-- Problem/Solution -->
    <div class="problem-solution">
        <h2>✅ Two powerful ways to connect servers</h2>
        <p><strong>📞 Phone Calls:</strong> Instantly call random Discord servers when yours is quiet. Perfect for spontaneous conversations and meeting new communities.</p>
        <p><strong>🌐 Community Hubs:</strong> Join permanent networks connecting multiple servers around shared interests. Gaming hubs, study groups, art communities, and more.</p>
    </div>

    <!-- Features Showcase -->
    <div class="features-showcase">
        <h2 class="showcase-title">See InterChat in Action</h2>

        <div class="slideshow-container">
            <!-- Hidden radio buttons for CSS-only control -->
            <input type="radio" name="slide" id="slide1" class="slide-radio" checked>
            <input type="radio" name="slide" id="slide2" class="slide-radio">
            <input type="radio" name="slide" id="slide3" class="slide-radio">

            <div class="slides-wrapper">
                <div class="slides-container">
                    <div class="slide">
                        <img src="https://i.postimg.cc/hGcPhDk4/asd.png" alt="Discord server phone calls" class="slide-image">
                        <div class="slide-content">
                            <h3 class="slide-title">📞 Discord Phone Calls</h3>
                            <p class="slide-description">Make instant phone calls between Discord servers. When your server is quiet, call random servers for spontaneous conversations and meet new communities.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <img src="https://www.interchat.tech/_next/image?url=%2Ffeatures%2Fcross-server-chat.png&w=640&q=75" alt="Cross-server messaging" class="slide-image">
                        <div class="slide-content">
                            <h3 class="slide-title">🌐 Cross-Server Communication</h3>
                            <p class="slide-description">Real-time messaging flows seamlessly between multiple Discord servers. Your community stays connected across server boundaries with instant communication.</p>
                        </div>
                    </div>

                    <div class="slide">
                        <img src="https://www.interchat.tech/_next/image?url=%2Ffeatures%2FHubDiscovery.png&w=640&q=75" alt="Community hub discovery" class="slide-image">
                        <div class="slide-content">
                            <h3 class="slide-title">🔍 Community Hub Networks</h3>
                            <p class="slide-description">Join thousands of themed community hubs connecting multiple servers. Find gaming groups, study communities, or create your own cross-server network.</p>
                        </div>
                    </div>
                </div>

                <!-- Removed navigation buttons for simplicity -->
            </div>

            <!-- Slide indicators -->
            <div class="slide-indicators">
                <label for="slide1" class="indicator-label indicator1"></label>
                <label for="slide2" class="indicator-label indicator2"></label>
                <label for="slide3" class="indicator-label indicator3"></label>
            </div>
        </div>
    </div>

    <!-- Quick Features -->
    <div class="quick-features">
        <div class="quick-feature">
            <span class="quick-feature-icon">📞</span>
            <h3>Server Phone Calls</h3>
            <p>Call random Discord servers instantly when yours is quiet</p>
        </div>

        <div class="quick-feature">
            <span class="quick-feature-icon">🌐</span>
            <h3>Community Hubs</h3>
            <p>Join themed networks connecting multiple servers together</p>
        </div>

        <div class="quick-feature">
            <span class="quick-feature-icon">⚡</span>
            <h3>2-Minute Setup</h3>
            <p>Add bot → Run /setup → Start calling and connecting</p>
        </div>

        <div class="quick-feature">
            <span class="quick-feature-icon">🛡️</span>
            <h3>Full Control</h3>
            <p>Complete moderation tools, admin dashboard, and safety features</p>
        </div>

        <div class="quick-feature">
            <span class="quick-feature-icon">🔒</span>
            <h3>Your Rules</h3>
            <p>Each server maintains its identity and moderation policies</p>
        </div>

        <div class="quick-feature">
            <span class="quick-feature-icon">🎯</span>
            <h3>Smart Matching</h3>
            <p>Find servers with similar interests for better phone call experiences</p>
        </div>
    </div>

    <!-- Dual Feature Highlight -->
    <div class="problem-solution">
        <h2>📞 Phone Calls: Instant Connections</h2>
        <p><strong>Server quiet at 3 AM?</strong> Use <code>/call</code> to instantly phone another Discord server anywhere in the world. Smart matching connects you with active communities looking for conversations. End calls anytime with <code>/hangup</code> - no commitment required.</p>

        <h2>🌐 Community Hubs: Always-On Networks</h2>
        <p><strong>Want ongoing connections?</strong> Join community hubs that permanently link multiple servers around shared interests. Gaming hubs with 50+ servers, study networks, art communities, and specialized topics. Use <code>/connect</code> to join or create your own hub.</p>

        <p><strong>Perfect for:</strong> Gaming servers • Study groups • Art communities • Tech discussions • Language learning • Any topic-based community</p>
    </div>

    <!-- Setup Guide -->
    <div class="setup-section">
        <h3 class="setup-title">🚀 Get Started in 2 Minutes</h3>
        <ol class="setup-steps">
            <li class="setup-step">Click "Invite" above to add InterChat to your Discord server</li>
            <li class="setup-step">Run <code>/setup</code> in your server for guided configuration</li>
            <li class="setup-step">Use <code>/call</code> for instant server calls or <code>/connect</code> to join community hubs</li>
            <li class="setup-step">Watch your community thrive with phone calls and hub connections!</li>
        </ol>
    </div>

    <!-- Testimonial -->
    <div class="testimonial">
        <p class="testimonial-text">"InterChat transformed our community! We use /call for instant connections when quiet, and joined a gaming hub that connects us with 40+ similar servers. Both features are incredible - it's like having the best of both worlds."</p>
        <p class="testimonial-author">— Gaming Community Admin, 2,500 members</p>
    </div>

    <!-- CTA Section -->
    <div class="cta-section">
        <h2 class="cta-title">Ready to Connect?</h2>
        <p class="cta-subtitle">Join 50,000+ servers using InterChat for phone calls, community hubs, and cross-server communication</p>

        <div class="cta-buttons">
            <a href="#" class="btn-primary">🚀 Add InterChat Now</a>
            <a href="#" class="btn-secondary">🗳️ Vote Every 12 Hours</a>
        </div>

        <p class="cta-footer">
            <strong>Free forever.</strong> Premium features for calls and hubs unlocked by voting every 12 hours.<br>
            Transform your Discord community today. ✨
        </p>
    </div>
</div>

<script>
let currentSlideIndex = 0;
const totalSlides = 3;

function changeSlide(direction) {
    currentSlideIndex += direction;

    if (currentSlideIndex >= totalSlides) {
        currentSlideIndex = 0;
    } else if (currentSlideIndex < 0) {
        currentSlideIndex = totalSlides - 1;
    }

    updateSlidePosition();
    updateIndicators();
}

function currentSlide(slideIndex) {
    currentSlideIndex = slideIndex - 1;
    updateSlidePosition();
    updateIndicators();
}

function updateSlidePosition() {
    const slidesContainer = document.getElementById('slidesContainer');
    const translateX = -currentSlideIndex * 33.333;
    slidesContainer.style.transform = `translateX(${translateX}%)`;
}

function updateIndicators() {
    const indicators = document.querySelectorAll('.indicator');
    indicators.forEach((indicator, index) => {
        if (index === currentSlideIndex) {
            indicator.classList.add('active');
        } else {
            indicator.classList.remove('active');
        }
    });
}

// Auto-advance slideshow every 5 seconds
setInterval(() => {
    changeSlide(1);
}, 5000);

// Initialize slideshow
document.addEventListener('DOMContentLoaded', function() {
    updateSlidePosition();
    updateIndicators();
});
</script>
