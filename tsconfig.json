{"compilerOptions": {"incremental": true, "composite": true, "target": "ESNext", "module": "NodeNext", "moduleResolution": "NodeNext", "resolveJsonModule": true, "allowJs": false, "declaration": true, "baseUrl": ".", "rootDir": "./src", "outDir": "./build", "esModuleInterop": true, "strict": true, "inlineSourceMap": true, "skipLibCheck": true, "useUnknownInCatchVariables": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "paths": {"#src/*": ["./src/*"], "#utils/*": ["./src/utils/*"], "#types/*.d.ts": ["src/types/*.d.ts"]}}, "include": ["src/**/*.ts", "src/**/*.json"], "exclude": ["node_modules", "build"], "packageManager": "bun@1.2.1"}