{"title": "InterChat Bot Metrics", "id": null, "tags": ["discord", "bot", "interchat"], "timezone": "browser", "schemaVersion": 36, "version": 1, "refresh": "10s", "panels": [{"id": 1, "type": "row", "title": "Overview", "collapsed": false, "gridPos": {"x": 0, "y": 0, "w": 24, "h": 1}}, {"id": 2, "title": "Messages Processed", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 6, "y": 1, "w": 6, "h": 6}, "options": {"reduceOptions": {"values": false, "calcs": ["last"], "fields": ""}, "orientation": "vertical", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"mappings": [], "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "blue"}]}}, "overrides": []}, "targets": [{"expr": "sum(increase(interchat_messages_total[24h]))", "legendFormat": "Messages (24h)", "refId": "A"}]}, {"id": 3, "title": "Total Guilds", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 1, "w": 6, "h": 6}, "options": {"reduceOptions": {"values": false, "calcs": ["last"], "fields": ""}, "orientation": "vertical", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"mappings": [], "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "purple"}]}}, "overrides": []}, "targets": [{"expr": "sum(interchat_guilds_total)", "legendFormat": "Guilds", "refId": "A"}]}, {"id": 4, "title": "Total Hubs", "type": "stat", "datasource": "Prometheus", "gridPos": {"x": 18, "y": 1, "w": 6, "h": 6}, "options": {"reduceOptions": {"values": false, "calcs": ["last"], "fields": ""}, "orientation": "vertical", "colorMode": "value", "graphMode": "area", "justifyMode": "auto"}, "fieldConfig": {"defaults": {"mappings": [], "color": {"mode": "palette-classic"}, "thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "orange"}]}}, "overrides": []}, "targets": [{"expr": "interchat_hubs_total", "legendFormat": "<PERSON><PERSON>", "refId": "A"}]}, {"id": 5, "title": "Messages Over Time", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 15, "w": 12, "h": 8}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}}, "overrides": []}, "targets": [{"expr": "sum(rate(interchat_messages_total[5m])) by (hub_id)", "legendFormat": "Hub: {{hub_id}}", "refId": "A"}]}, {"id": 6, "type": "row", "title": "Cluster Metrics", "collapsed": false, "gridPos": {"x": 0, "y": 23, "w": 24, "h": 1}}, {"id": 7, "title": "Guild Count by Cluster", "type": "barchart", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 16, "w": 12, "h": 8}, "options": {"orientation": "horizontal", "legend": {"showLegend": false}, "text": {"valueSize": 100}}, "fieldConfig": {"defaults": {"custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "targets": [{"expr": "interchat_guilds_total", "legendFormat": "Cluster {{cluster}}", "refId": "A"}]}, {"id": 8, "title": "Shard Status", "type": "status-history", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 16, "w": 12, "h": 8}, "options": {"legend": {"showLegend": true}, "colWidth": 0.9, "rowHeight": 0.9, "showValue": "auto", "rowsFrame": {"layout": "auto"}, "colsFrame": {"layout": "auto"}, "valueDisplayMode": "color"}, "fieldConfig": {"defaults": {"thresholds": {"mode": "absolute", "steps": [{"value": null, "color": "red"}, {"value": 0.5, "color": "green"}]}, "mappings": [{"type": "value", "options": {"0": {"text": "Down", "color": "red"}, "1": {"text": "Up", "color": "green"}}}], "color": {"mode": "thresholds"}}, "overrides": []}, "targets": [{"expr": "interchat_shards_status", "legendFormat": "C{{cluster}} S{{shard}}", "refId": "A"}]}, {"id": 9, "type": "row", "title": "System Metrics", "collapsed": false, "gridPos": {"x": 0, "y": 24, "w": 24, "h": 1}}, {"id": 10, "title": "CPU Usage", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 25, "w": 12, "h": 8}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}}, "overrides": []}, "targets": [{"expr": "rate(process_cpu_seconds_total[1m]) * 100", "legendFormat": "CPU %", "refId": "A"}]}, {"id": 11, "title": "Memory Usage", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 12, "y": 25, "w": 12, "h": 8}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}, "unit": "bytes"}, "overrides": []}, "targets": [{"expr": "process_resident_memory_bytes", "legendFormat": "Memory", "refId": "A"}]}, {"id": 12, "title": "Commands Over Time", "type": "timeseries", "datasource": "Prometheus", "gridPos": {"x": 0, "y": 7, "w": 12, "h": 8}, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "fieldConfig": {"defaults": {"custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"tooltip": false, "viz": false, "legend": false}, "thresholdsStyle": {"mode": "off"}}, "color": {"mode": "palette-classic"}}, "overrides": []}, "targets": [{"expr": "sum(rate(interchat_commands_total[5m])) by (command)", "legendFormat": "{{command}}", "refId": "A"}]}, {"id": 13, "title": "Most Used Commands", "type": "barchart", "datasource": "Prometheus", "targets": [{"expr": "sum by (command) (bot_command_usage_total)", "legendFormat": "{{command}}", "refId": "A"}], "options": {"orientation": "vertical", "barWidth": 0.9, "fillOpacity": 80, "stacking": {"mode": "none"}}, "gridPos": {"x": 0, "y": 0, "w": 24, "h": 8}}], "time": {"from": "now-3h", "to": "now"}, "editable": true}