# Media Sharing System - Testing Guide

This document provides comprehensive testing instructions for the new media sharing system in InterChat calls.

## Overview

The media sharing system allows users to share GIFs, stickers, and images during calls with the following features:
- **Initial Allowance**: 2 media items per call session
- **Top.gg Voting**: Vote on Top.gg to get 5 additional media items (12-hour cooldown)
- **Premium Upgrade**: Unlimited media sharing via Ko-fi donation ($2.99+/month)

## Prerequisites

1. Ensure the database migration has been applied:
   ```bash
   npx prisma migrate deploy
   ```

2. Verify the following environment variables are set:
   - `DISCORD_TOKEN`
   - `TOPGG_TOKEN` (for vote verification)
   - `KOFI_WEBHOOK_SECRET` (for donation processing)

## Manual Testing Scenarios

### 1. Basic Media Sharing

**Test Case**: User shares media within initial allowance
1. Start a call using `/call`
2. Share first media item: `.media https://tenor.com/view/example-gif`
3. Share second media item: `.media https://giphy.com/gifs/example`
4. Verify both commands succeed and show remaining count

**Expected Results**:
- First share: "✅ Media shared successfully! 1 remaining."
- Second share: "✅ Media shared successfully! 0 remaining."

### 2. Limit Enforcement

**Test Case**: User exceeds initial allowance
1. Continue from previous test (0 remaining)
2. Try to share third media item: `.media https://tenor.com/view/another-gif`

**Expected Results**:
- Error message with Top.gg voting prompt
- Button to "Vote on Top.gg"
- Button to "Upgrade to Premium"

### 3. Status Checking

**Test Case**: Check media usage status
1. Use command: `.media status`

**Expected Results**:
- Shows current usage: "📊 Usage: 2/2 media items used"
- Shows remaining: "📈 Remaining: 0 items"
- Shows voting prompt if applicable

### 4. Top.gg Voting Integration

**Test Case**: Vote on Top.gg for additional media
1. When limit reached, click "Vote on Top.gg" button
2. Complete vote on Top.gg website
3. Wait for webhook to process (may take a few minutes)
4. Try sharing media again

**Expected Results**:
- After successful vote: User gets 5 additional media items (total 7 per call)
- Vote cooldown: 12 hours before next vote

### 5. Premium Functionality

**Test Case**: Premium user unlimited access
1. Set up test user with premium status:
   ```sql
   UPDATE "User" SET "hasMediaPremium" = true WHERE id = 'USER_ID';
   ```
2. Start a call and share multiple media items

**Expected Results**:
- No limits enforced
- Status shows: "⭐ Premium Active - Unlimited media sharing!"

### 6. Ko-fi Integration

**Test Case**: Premium upgrade via donation
1. Click "Upgrade to Premium" button
2. Complete donation on Ko-fi ($2.99+ for monthly, $2.990+ for yearly)
3. Verify webhook processes donation
4. Check user's premium status

**Expected Results**:
- $2.99 donation: 30 days of premium
- $2.990 donation: 365 days of premium
- Automatic premium activation

### 7. Call Session Reset

**Test Case**: Media limits reset on new call
1. Use all media items in one call
2. End the call
3. Start a new call
4. Check media status

**Expected Results**:
- New call resets usage to 0/2
- Previous call's usage doesn't carry over

### 8. Rate Limiting

**Test Case**: Command rate limiting
1. Execute media commands rapidly
2. Verify rate limiting kicks in

**Expected Results**:
- 12-second cooldown between media commands
- Clear error message with remaining time

### 9. Error Handling

**Test Case**: Invalid media URLs
1. Try sharing invalid URL: `.media https://example.com/not-media`
2. Try sharing non-media URL: `.media https://google.com`

**Expected Results**:
- Clear error messages
- Suggestions for valid media types

### 10. Command Aliases

**Test Case**: Alternative command usage
1. Test aliases: `.m`, `.gif`, `.img`
2. Test different actions: `.media vote`, `.media premium`

**Expected Results**:
- All aliases work identically
- Action parameters function correctly

## Performance Testing

### Response Time Verification

Monitor command response times to ensure they meet the sub-1-second requirement:

1. Enable debug logging
2. Execute media commands
3. Check logs for timing information

**Expected Results**:
- Media commands complete in <500ms
- Database queries optimized with proper indexing
- Redis caching reduces repeated lookups

### Load Testing

Test with multiple concurrent users:

1. Simulate multiple users in different calls
2. Execute media commands simultaneously
3. Monitor system performance

**Expected Results**:
- No performance degradation
- Proper cache isolation between users/calls
- Database connections handled efficiently

## Database Verification

### Check Data Integrity

Verify data is properly stored:

```sql
-- Check user premium status
SELECT id, "hasMediaPremium", "mediaPremiumExpiresAt" FROM "User" WHERE "hasMediaPremium" = true;

-- Check donation records
SELECT * FROM "Donation" WHERE "amountUsd" >= 3 ORDER BY "createdAt" DESC LIMIT 5;
```

### Redis Verification

Check Redis entries for media tracking:

```bash
# Check media usage counts per call
redis-cli KEYS "media:count:*"

# Check vote cooldowns
redis-cli KEYS "media:vote_cooldown:*"

# Check session tracking
redis-cli KEYS "media:session_reset:*"

# Check media usage logs
redis-cli KEYS "media:count:log:*"

# View specific usage count
redis-cli GET "media:count:CALL_ID:USER_ID"

# View vote cooldown TTL
redis-cli TTL "media:vote_cooldown:USER_ID"
```

## Troubleshooting

### Common Issues

1. **Migration Errors**: Ensure database schema is up to date
2. **Cache Issues**: Clear Redis cache if needed: `redis-cli FLUSHDB`
3. **Webhook Issues**: Verify Ko-fi webhook URL and secret
4. **Permission Issues**: Check bot permissions in test servers

### Debug Commands

Enable debug logging to troubleshoot issues:

```bash
# Set log level to debug
export LOG_LEVEL=debug

# Monitor specific components
export DEBUG=media:*,donation:*,vote:*
```

## Success Criteria

The media sharing system passes testing when:

- ✅ All basic functionality works as expected
- ✅ Rate limiting prevents spam
- ✅ Top.gg voting integration functions correctly
- ✅ Ko-fi premium upgrades process automatically
- ✅ Performance targets are met (<1s response times)
- ✅ Error handling is graceful and informative
- ✅ Database integrity is maintained
- ✅ Cache invalidation works properly

## Reporting Issues

When reporting issues, include:

1. Steps to reproduce
2. Expected vs actual behavior
3. Relevant log entries
4. Database state (if applicable)
5. User IDs and call IDs involved
