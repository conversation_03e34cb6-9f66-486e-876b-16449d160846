# Media Sharing in InterChat Calls

Share GIFs, stickers, and images during your InterChat calls with our new media sharing system!

## Quick Start

### Basic Commands

- `.media <url>` - Share a media URL
- `.gif <url>` - Share a GIF (alias for media)
- `.m status` - Check your media usage
- `.media premium` - Learn about premium features

### Example Usage

```
i.media https://tenor.com/view/excited-happy-gif
i.gif https://giphy.com/gifs/celebration-party
i.m status
```

## How It Works

### Free Tier
- **2 media items** per call session
- Resets when you start a new call
- Perfect for casual sharing

### Vote for More
- **Vote on Top.gg** to get 5 additional items (total 7 per call)
- 12-hour cooldown between votes
- Quick and easy way to get more media

### Premium Unlimited
- **$2.99/month** or **$2.990/year** via Ko-fi
- Unlimited media sharing in all calls
- Support InterChat development

## Supported Media Types

### ✅ Supported
- **GIFs**: <PERSON>or, <PERSON><PERSON><PERSON>, direct .gif URLs
- **Images**: PNG, JPG, JPEG, WebP
- **Stickers**: Discord sticker URLs

### ❌ Not Supported
- Video files (MP4, MOV, etc.)
- Audio files
- Documents or other file types

## Commands Reference

### Primary Commands

| Command | Description | Example |
|---------|-------------|---------|
| `.media <url>` | Share any supported media | `.media https://tenor.com/view/example` |
| `.gif <url>` | Share a GIF specifically | `.gif https://giphy.com/gifs/example` |
| `.m status` | Check usage and remaining items | `.m status` |
| `.media vote` | Get Top.gg voting link | `.media vote` |
| `.media premium` | Learn about premium upgrade | `.media premium` |

### Status Information

When you check status, you'll see:
- Current usage (e.g., "2/2 items used")
- Remaining items available
- Premium status (if applicable)
- Voting availability

## Getting More Media Items

### Option 1: Vote on Top.gg (Free)
1. Use `.media vote` or click the vote button when prompted
2. Vote for InterChat on Top.gg
3. Get 5 additional media items automatically
4. Can vote again after 12 hours

### Option 2: Upgrade to Premium
1. Use `.media premium` to see upgrade options
2. Donate $2.99+ monthly or $2.990+ yearly on Ko-fi
3. Get unlimited media sharing immediately
4. Support InterChat's development

## Tips & Best Practices

### Finding Great Media
- **Tenor**: Search for GIFs with reactions and emotions
- **Giphy**: Wide variety of animated content
- **Discord**: Use existing stickers from servers

### Sharing Etiquette
- Keep media appropriate for the call context
- Don't spam - respect the rate limits
- Consider other participants' preferences

### Troubleshooting
- **Invalid URL**: Make sure it's a direct link to media
- **Rate Limited**: Wait 12 seconds between commands
- **No Call Active**: You must be in a call to share media

## Premium Benefits

### What You Get
- ⭐ **Unlimited Media**: No limits on GIFs, stickers, or images
- 🚀 **Instant Access**: No waiting or voting required
- 💝 **Support Development**: Help keep InterChat running
- 🎯 **Priority Support**: Get help faster when needed

### Pricing
- **Monthly**: $2.99/month - Perfect for regular users
- **Yearly**: $2.990/year - Best value (save $6!)

### How to Upgrade
1. Click "Upgrade to Premium" when prompted
2. Visit our Ko-fi page
3. Choose your preferred plan
4. Premium activates automatically after payment

## Frequently Asked Questions

### Q: Do media limits reset between calls?
A: Yes! Each new call gives you a fresh 2 media items to start.

### Q: Can I share the same media multiple times?
A: Yes, but each share counts toward your limit.

### Q: What happens if my premium expires?
A: You'll return to the free tier (2 items + voting) but keep access to previously shared media.

### Q: Can I vote multiple times per day?
A: You can vote once every 12 hours on Top.gg for additional media items.

### Q: Are there any content restrictions?
A: Yes, media must be appropriate and follow Discord's Terms of Service.

### Q: Can I use this in private calls?
A: Yes! Media sharing works in all InterChat calls, both hub calls and private server connections.

## Support

Need help with media sharing?

- **Command Help**: Use `.help media` for quick reference
- **Support Server**: Join our Discord for assistance
- **Bug Reports**: Report issues on our GitHub
- **Feature Requests**: Suggest improvements in our community

## Updates & Changes

This feature is actively developed. Check our:
- **Changelog**: For recent updates
- **Announcements**: In our support server
- **Documentation**: For the latest features

---

*Happy sharing! 🎉*
