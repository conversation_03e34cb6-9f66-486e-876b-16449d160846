// Test script to validate Ko-fi webhook examples with Zod schema
import { kofiPayloadSchema } from './src/lib/donations/schemas/kofi.js';

// Example 1: Subsequent Payment for a Membership Tier
const subsequentPayment = {
  "verification_token": "05ede22e-9a69-45f9-a234-402fda2afca2",
  "message_id": "584a76e2-eb4e-4a9c-a340-9e2ee94cef4c",
  "timestamp": "2025-06-24T06:47:43Z",
  "type": "Subscription",
  "is_public": true,
  "from_name": "Jo Example",
  "message": null,
  "amount": "5.00",
  "url": "https://ko-fi.com/Home/CoffeeShop?txid=00000000-1111-2222-3333-444444444444",
  "email": "<EMAIL>",
  "currency": "USD",
  "is_subscription_payment": true,
  "is_first_subscription_payment": false,
  "kofi_transaction_id": "00000000-1111-2222-3333-444444444444",
  "shop_items": null,
  "tier_name": "Bronze",
  "shipping": null
};

// Example 2: First Payment of a Monthly Subscription
const firstPayment = {
  "verification_token": "05ede22e-9a69-45f9-a234-402fda2afca2",
  "message_id": "a9835bdd-6828-4f91-bd96-428d4cfbbe18",
  "timestamp": "2025-06-24T06:47:43Z",
  "type": "Subscription",
  "is_public": true,
  "from_name": "Jo Example",
  "message": "Good luck with the integration!",
  "amount": "3.00",
  "url": "https://ko-fi.com/Home/CoffeeShop?txid=00000000-1111-2222-3333-444444444444",
  "email": "<EMAIL>",
  "currency": "USD",
  "is_subscription_payment": true,
  "is_first_subscription_payment": true,
  "kofi_transaction_id": "00000000-1111-2222-3333-444444444444",
  "shop_items": null,
  "tier_name": null,
  "shipping": null
};

// Example 3: Single Donation
const singleDonation = {
  "verification_token": "05ede22e-9a69-45f9-a234-402fda2afca2",
  "message_id": "ded4b9d0-4fb2-4b9c-a4bf-9738b298abf6",
  "timestamp": "2025-06-24T06:47:43Z",
  "type": "Donation",
  "is_public": true,
  "from_name": "Jo Example",
  "message": "Good luck with the integration!",
  "amount": "3.00",
  "url": "https://ko-fi.com/Home/CoffeeShop?txid=00000000-1111-2222-3333-444444444444",
  "email": "<EMAIL>",
  "currency": "USD",
  "is_subscription_payment": false,
  "is_first_subscription_payment": false,
  "kofi_transaction_id": "00000000-1111-2222-3333-444444444444",
  "shop_items": null,
  "tier_name": null,
  "shipping": null
};

// Test validation
console.log('Testing Ko-fi payload validation...\n');

const testCases = [
  { name: 'Subsequent Payment', data: subsequentPayment },
  { name: 'First Payment', data: firstPayment },
  { name: 'Single Donation', data: singleDonation }
];

for (const testCase of testCases) {
  console.log(`Testing ${testCase.name}:`);
  try {
    const result = kofiPayloadSchema.parse(testCase.data);
    console.log('✅ Validation passed');
    console.log(`   Type: ${result.type}`);
    console.log(`   Amount: ${result.amount} ${result.currency}`);
    console.log(`   Subscription: ${result.is_subscription_payment}`);
    console.log(`   First Payment: ${result.is_first_subscription_payment}`);
    console.log(`   Tier: ${result.tier_name || 'None'}`);
  } catch (error) {
    console.log('❌ Validation failed:');
    console.log(error.message);
  }
  console.log('');
}
