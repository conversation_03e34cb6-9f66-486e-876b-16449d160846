# InterChat Call Notification Enhancements - Test Plan

## Overview
This document outlines the testing plan for the two new call notification features:
1. Call End Report Button
2. Call Answer Ping Notification

## Performance Requirements
- Sub-1-second command responses
- Sub-10-second call matching
- Cross-cluster compatibility

## Test Cases

### 1. Call End Report Button

#### Test 1.1: Report Button Appears
- **Action**: End a call using `/hangup` command
- **Expected**: Call end notification shows both "New Call" and "Report" buttons
- **Performance**: Response time should be <500ms

#### Test 1.2: Report Button Functionality
- **Action**: Click the "Report" button on call end notification
- **Expected**: Report reason dropdown appears (existing ReportCall handler)
- **Performance**: Button response should be immediate

#### Test 1.3: Report Submission
- **Action**: Select a reason and submit report
- **Expected**: Report is sent to moderation channel with proper call ID
- **Performance**: Report submission should complete within 2 seconds

### 2. Call Answer Ping Notification

#### Test 2.1: Ping on Call Match
- **Action**: Two users initiate calls and get matched
- **Expected**: Both users receive "Call Connected!" notification with ping mention
- **Performance**: Notification should arrive within 1 second of match

#### Test 2.2: Ping Format
- **Action**: Check the notification content
- **Expected**: Message starts with `<@userId>` followed by call connected message
- **Performance**: No additional latency from mention

#### Test 2.3: Cross-Cluster Compatibility
- **Action**: Test calls between users on different bot clusters
- **Expected**: Pings work correctly across clusters
- **Performance**: No degradation in cross-cluster performance

### 3. Performance Tests

#### Test 3.1: Command Response Time
- **Action**: Measure `/call`, `/hangup`, `/skip` response times
- **Expected**: All commands respond in <500ms (improved from 1000ms target)
- **Method**: Use built-in timing logs

#### Test 3.2: Call Matching Time
- **Action**: Measure time from queue entry to match notification
- **Expected**: <10 seconds for 90% of requests
- **Method**: Monitor matching engine logs

#### Test 3.3: Memory Usage
- **Action**: Monitor memory usage with new notification features
- **Expected**: No significant increase in memory consumption
- **Method**: Check cluster memory usage

### 4. Cross-Cluster Tests

#### Test 4.1: Distributed State Consistency
- **Action**: Verify call state is consistent across clusters
- **Expected**: All clusters have same view of active calls
- **Performance**: State sync should not add >100ms latency

#### Test 4.2: Notification Delivery
- **Action**: Test notifications across different cluster configurations
- **Expected**: All participants receive notifications regardless of cluster
- **Performance**: Cross-cluster notifications within 1 second

## Implementation Notes

### Changes Made:
1. **NotificationService.ts**: 
   - Enhanced `notifyCallEnded()` to include Report button
   - Enhanced `notifyCallMatched()` to include user ping
   - Added `sendMessageWithContent()` method for ping functionality

2. **Integration**: 
   - Report button uses existing `report_call` custom ID pattern
   - Leverages existing ReportCall handler (no changes needed)
   - Uses existing BroadcastService.sendMessage method

### Performance Optimizations:
- Minimal additional overhead (one button, one mention)
- Reuses existing webhook infrastructure
- No new database queries or Redis operations
- Follows existing allowedMentions patterns

## Success Criteria
- ✅ All tests pass
- ✅ Performance targets maintained
- ✅ Cross-cluster compatibility verified
- ✅ No regression in existing functionality
- ✅ Report integration works with moderation dashboard
