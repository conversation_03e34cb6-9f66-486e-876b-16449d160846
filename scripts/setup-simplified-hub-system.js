#!/usr/bin/env node

/**
 * Setup script for the Simplified Hub System
 * This script initializes the daily challenge system and sets up background jobs
 */

import { PrismaClient } from '@prisma/client';
import { DailyChallengeService } from '../src/services/DailyChallengeService.js';

const db = new PrismaClient();
const challengeService = new DailyChallengeService();

async function main() {
  console.log('🚀 Setting up Simplified Hub System...\n');

  try {
    // 1. Initialize daily challenges for today
    console.log('📅 Initializing daily challenges...');
    await challengeService.generateTodaysChallenges();
    console.log('✅ Daily challenges initialized\n');

    // 2. Create sample user data for testing (optional)
    if (process.argv.includes('--sample-data')) {
      console.log('👥 Creating sample user data...');
      await createSampleData();
      console.log('✅ Sample data created\n');
    }

    // 3. Verify database schema
    console.log('🗄️ Verifying database schema...');
    await verifySchema();
    console.log('✅ Database schema verified\n');

    // 4. Display setup instructions
    displaySetupInstructions();

    console.log('🎉 Simplified Hub System setup complete!');
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

async function createSampleData() {
  // Create sample interests for testing
  const sampleInterests = ['gaming', 'technology', 'art', 'music', 'sports'];
  
  // Update a few existing users with sample data
  const users = await db.user.findMany({ take: 5 });
  
  for (const user of users) {
    const randomInterests = sampleInterests
      .sort(() => 0.5 - Math.random())
      .slice(0, Math.floor(Math.random() * 3) + 1);
    
    await db.user.update({
      where: { id: user.id },
      data: {
        interests: randomInterests,
        activityLevel: ['CASUAL', 'REGULAR', 'ACTIVE'][Math.floor(Math.random() * 3)],
        currentStreak: Math.floor(Math.random() * 10),
        longestStreak: Math.floor(Math.random() * 30),
      },
    });
  }
  
  console.log(`   Updated ${users.length} users with sample profile data`);
}

async function verifySchema() {
  // Check if required tables exist
  const tables = ['UserConnection', 'DailyChallenge', 'UserDailyProgress'];
  
  for (const table of tables) {
    try {
      await db.$queryRaw`SELECT 1 FROM ${table} LIMIT 1`;
      console.log(`   ✓ Table ${table} exists`);
    } catch (error) {
      throw new Error(`Table ${table} does not exist. Please run migrations first.`);
    }
  }
  
  // Check if User table has new columns
  try {
    await db.$queryRaw`SELECT "interests", "activityLevel", "currentStreak" FROM "User" LIMIT 1`;
    console.log('   ✓ User table has new columns');
  } catch (error) {
    throw new Error('User table missing new columns. Please run migrations first.');
  }
}

function displaySetupInstructions() {
  console.log('📋 Next Steps:\n');
  
  console.log('1. Set up cron jobs for background tasks:');
  console.log('   # Generate daily challenges (midnight UTC)');
  console.log('   0 0 * * * cd /path/to/interchat && node scripts/generate-daily-challenges.js\n');
  
  console.log('   # Update user activity levels (every 6 hours)');
  console.log('   0 */6 * * * cd /path/to/interchat && node scripts/update-activity-levels.js\n');
  
  console.log('   # Clean up expired data (2 AM daily)');
  console.log('   0 2 * * * cd /path/to/interchat && node scripts/cleanup-expired-data.js\n');
  
  console.log('2. Monitor key metrics:');
  console.log('   - Daily challenge completion rates');
  console.log('   - User streak distributions');
  console.log('   - Hub recommendation success rates');
  console.log('   - Friend connection growth\n');
  
  console.log('3. Test the new features:');
  console.log('   - /setup (with interest selection for new users)');
  console.log('   - /connect (with personalized recommendations)');
  console.log('   - /challenges (complete daily challenge system)\n');
  
  console.log('4. Enable feature flags in your config:');
  console.log('   DAILY_CHALLENGES=true');
  console.log('   HUB_RECOMMENDATIONS=true');
  console.log('   FRIEND_SYSTEM=true');
  console.log('   INTEREST_ONBOARDING=true\n');
}

// Run the setup
main().catch(console.error);
