#!/usr/bin/env node

// @ts-check

/**
 * User Activity Level Update Script
 * Runs every 6 hours to update user activity levels based on recent engagement
*/

import { PrismaClient } from '../build/generated/prisma/client/client.js';
import { UserProfileService } from '../build/services/UserProfileService.js';

const db = new PrismaClient();
const profileService = new UserProfileService();

async function main() {
  console.log(`[${new Date().toISOString()}] Starting activity level updates...`);

  try {
    // Get all users who have been active in the last 30 days
    const activeUsers = await db.user.findMany({
      where: {
        lastActive: {
          gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        },
      },
      select: {
        id: true,
        messageCount: true,
        activityLevel: true,
        lastActive: true,
      },
    });

    console.log(`📊 Processing ${activeUsers.length} active users...`);

    let updatedCount = 0;
    const activityChanges = {
      CASUAL: 0,
      REGULAR: 0,
      ACTIVE: 0,
      POWER_USER: 0,
    };

    for (const user of activeUsers) {
      const newActivityLevel = calculateActivityLevel(user);

      if (newActivityLevel !== user.activityLevel) {
        await db.user.update({
          where: { id: user.id },
          data: { activityLevel: newActivityLevel },
        });

        activityChanges[newActivityLevel]++;
        updatedCount++;
      }
    }

    console.log(`✅ Updated ${updatedCount} users' activity levels:`);
    console.log(`   CASUAL: ${activityChanges.CASUAL} users`);
    console.log(`   REGULAR: ${activityChanges.REGULAR} users`);
    console.log(`   ACTIVE: ${activityChanges.ACTIVE} users`);
    console.log(`   POWER_USER: ${activityChanges.POWER_USER} users`);

    console.log('✅ Activity level updates completed successfully');
  } catch (error) {
    console.error('❌ Activity level update failed:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

function calculateActivityLevel(user) {
  const daysSinceLastActive = Math.floor(
    (Date.now() - new Date(user.lastActive).getTime()) / (1000 * 60 * 60 * 24)
  );

  // If user hasn't been active in 7+ days, downgrade to CASUAL
  if (daysSinceLastActive >= 7) {
    return 'CASUAL';
  }

  // Calculate activity score based on multiple factors
  let score = 0;

  // Message count factor (normalized)
  if (user.messageCount >= 1000) score += 30;
  else if (user.messageCount >= 500) score += 20;
  else if (user.messageCount >= 100) score += 10;
  else if (user.messageCount >= 10) score += 5;

  // Current streak factor
  if (user.currentStreak >= 30) score += 25;
  else if (user.currentStreak >= 14) score += 20;
  else if (user.currentStreak >= 7) score += 15;
  else if (user.currentStreak >= 3) score += 10;

  // Recent activity factor
  if (daysSinceLastActive === 0) score += 15; // Active today
  else if (daysSinceLastActive <= 1) score += 10; // Active yesterday
  else if (daysSinceLastActive <= 3) score += 5; // Active in last 3 days

  // Determine activity level based on score
  if (score >= 60) return 'POWER_USER';
  if (score >= 35) return 'ACTIVE';
  if (score >= 15) return 'REGULAR';
  return 'CASUAL';
}

main();
