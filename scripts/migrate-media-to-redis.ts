#!/usr/bin/env bun

/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Migration script to remove media sharing database tables
 * and confirm Redis-based system is ready
 */

import { PrismaClient } from '@prisma/client';
import { getRedis } from '../build/utils/Redis.js';
import { RedisKeys } from '../build/utils/Constants.js';

const prisma = new PrismaClient();
const redis = getRedis();

interface MigrationOptions {
  dryRun: boolean;
  backup: boolean;
}

async function main() {
  const args = process.argv.slice(2);
  const options: MigrationOptions = {
    dryRun: !args.includes('--execute'),
    backup: !args.includes('--no-backup'),
  };

  console.log('🚀 InterChat Media Sharing Migration to Redis');
  console.log('============================================');
  console.log(`Mode: ${options.dryRun ? 'DRY RUN' : 'EXECUTE'}`);
  console.log(`Backup: ${options.backup ? 'YES' : 'NO'}`);
  console.log('');

  try {
    // Step 1: Check if CallMediaUsage table exists
    console.log('📊 Checking current database state...');
    
    let mediaUsageCount = 0;
    try {
      // Try to count records in CallMediaUsage table
      const result = await prisma.$queryRaw`
        SELECT COUNT(*) as count 
        FROM "CallMediaUsage"
      ` as [{ count: bigint }];
      
      mediaUsageCount = Number(result[0].count);
      console.log(`   Found ${mediaUsageCount} media usage records`);
    } catch (error) {
      if (error.message.includes('relation "CallMediaUsage" does not exist')) {
        console.log('   ✅ CallMediaUsage table already removed');
        mediaUsageCount = 0;
      } else {
        throw error;
      }
    }

    // Step 2: Backup existing data if requested and data exists
    if (options.backup && mediaUsageCount > 0) {
      console.log('💾 Creating backup of media usage data...');
      
      if (!options.dryRun) {
        const backupData = await prisma.$queryRaw`
          SELECT * FROM "CallMediaUsage" ORDER BY "usedAt" DESC
        `;
        
        const backupFile = `media-usage-backup-${Date.now()}.json`;
        await Bun.write(backupFile, JSON.stringify(backupData, null, 2));
        console.log(`   ✅ Backup saved to ${backupFile}`);
      } else {
        console.log('   📝 Would create backup file');
      }
    }

    // Step 3: Test Redis connectivity
    console.log('🔗 Testing Redis connectivity...');
    
    const testKey = `${RedisKeys.MediaUsageCount}:test:migration`;
    await redis.setex(testKey, 10, 'test');
    const testValue = await redis.get(testKey);
    await redis.del(testKey);
    
    if (testValue === 'test') {
      console.log('   ✅ Redis connection working');
    } else {
      throw new Error('Redis test failed');
    }

    // Step 4: Check Redis keys structure
    console.log('🔍 Checking Redis key structure...');
    
    const existingKeys = await redis.keys(`${RedisKeys.MediaUsageCount}:*`);
    console.log(`   Found ${existingKeys.length} existing media usage keys in Redis`);
    
    const voteKeys = await redis.keys(`${RedisKeys.MediaVoteCooldown}:*`);
    console.log(`   Found ${voteKeys.length} existing vote cooldown keys in Redis`);

    // Step 5: Drop database tables if they exist
    if (mediaUsageCount > 0 || !options.dryRun) {
      console.log('🗑️  Removing database tables...');
      
      if (!options.dryRun) {
        try {
          // Drop CallMediaUsage table
          await prisma.$executeRaw`DROP TABLE IF EXISTS "CallMediaUsage" CASCADE`;
          console.log('   ✅ Dropped CallMediaUsage table');
          
          // Drop MediaType enum
          await prisma.$executeRaw`DROP TYPE IF EXISTS "MediaType" CASCADE`;
          console.log('   ✅ Dropped MediaType enum');
          
        } catch (error) {
          console.log(`   ⚠️  Table removal: ${error.message}`);
        }
      } else {
        console.log('   📝 Would drop CallMediaUsage table and MediaType enum');
      }
    }

    // Step 6: Verify migration
    console.log('✅ Verifying migration...');
    
    try {
      await prisma.$queryRaw`SELECT 1 FROM "CallMediaUsage" LIMIT 1`;
      console.log('   ⚠️  CallMediaUsage table still exists');
    } catch (error) {
      if (error.message.includes('relation "CallMediaUsage" does not exist')) {
        console.log('   ✅ CallMediaUsage table successfully removed');
      }
    }

    // Step 7: Test MediaUsageService
    console.log('🧪 Testing MediaUsageService...');
    
    if (!options.dryRun) {
      const { default: MediaUsageService } = await import('../build/services/MediaUsageService.js');
      const mediaService = new MediaUsageService();
      
      const testCallId = 'test-call-migration';
      const testUserId = 'test-user-migration';
      
      // Test getting stats
      const stats = await mediaService.getMediaUsageStats(testCallId, testUserId);
      console.log(`   ✅ MediaUsageService working - User has ${stats.remaining} media items remaining`);
      
      // Test vote cooldown
      const canVote = await mediaService.canUserVoteOnTopgg(testUserId);
      console.log(`   ✅ Vote system working - User can vote: ${canVote}`);
      
      // Clean up test data
      await mediaService.resetCallMediaUsage(testCallId);
    } else {
      console.log('   📝 Would test MediaUsageService functionality');
    }

    console.log('');
    console.log('🎉 Migration completed successfully!');
    console.log('');
    console.log('📋 Summary:');
    console.log(`   • Database tables: ${mediaUsageCount > 0 ? 'Removed' : 'Already clean'}`);
    console.log(`   • Redis system: Working`);
    console.log(`   • MediaUsageService: ${options.dryRun ? 'Not tested (dry run)' : 'Working'}`);
    console.log('');
    console.log('🚀 Media sharing is now fully Redis-based!');
    
    if (options.dryRun) {
      console.log('');
      console.log('To execute the migration, run:');
      console.log('bun scripts/migrate-media-to-redis.ts --execute');
    }

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    await redis.quit();
  }
}

// Handle script execution
if (import.meta.main) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
