/*
 * Copyright (C) 2025 InterChat
 *
 * InterChat is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published
 * by the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * InterChat is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with InterChat.  If not, see <https://www.gnu.org/licenses/>.
 */

/**
 * Enhanced Metrics Setup Script
 *
 * This script helps integrate the new metrics collection throughout the InterChat codebase.
 * It provides utility functions for tracking business metrics and performance indicators.
 */

import { performance } from 'perf_hooks';
import Logger from '#utils/Logger.js';

/**
 * Metrics tracking utilities for enhanced monitoring
 */
export class MetricsTracker {
  constructor(metricsService) {
    this.metricsService = metricsService;
  }

  /**
   * Track user engagement metrics
   */
  async trackUserActivity(userId, activityType = 'message') {
    try {
      // Track daily active users
      const today = new Date().toISOString().split('T')[0];
      const activeUsersKey = `active_users:${today}`;

      // This would typically use Redis to track unique users
      // For now, we'll increment the gauge directly
      this.metricsService.trackUserEngagement('active_users', '24h', 1);

      // Track feature usage
      this.metricsService.trackFeatureUsage(activityType, 'regular_user');

      Logger.debug(`Tracked user activity: ${userId} - ${activityType}`);
    } catch (error) {
      Logger.error('Failed to track user activity:', error);
    }
  }

  /**
   * Track hub performance and health
   */
  async trackHubMetrics(hubId, hubName, messageCount, connectionCount) {
    try {
      // Track hub activity
      this.metricsService.trackHubActivity(hubId, hubName, 'message_count', messageCount);
      this.metricsService.trackHubActivity(hubId, hubName, 'connection_count', connectionCount);

      // Calculate hub health score (example: based on activity and connections)
      const healthScore = Math.min(1, (messageCount / 100) * (connectionCount / 10));
      this.metricsService.trackHubActivity(hubId, hubName, 'health_score', healthScore);

      Logger.debug(`Tracked hub metrics: ${hubName} - Messages: ${messageCount}, Connections: ${connectionCount}`);
    } catch (error) {
      Logger.error('Failed to track hub metrics:', error);
    }
  }

  /**
   * Track connection health and webhook delivery
   */
  async trackConnectionHealth(serverId, hubId, isHealthy, webhookSuccess = true) {
    try {
      const status = isHealthy ? 'healthy' : 'unhealthy';
      this.metricsService.trackConnectionHealth(serverId, hubId, status, isHealthy ? 1 : 0);

      // Track webhook delivery
      const deliveryStatus = webhookSuccess ? 'success' : 'failure';
      this.metricsService.trackWebhookDelivery(deliveryStatus, hubId);

      Logger.debug(`Tracked connection health: ${serverId} -> ${hubId} - ${status}`);
    } catch (error) {
      Logger.error('Failed to track connection health:', error);
    }
  }

  /**
   * Track API performance with timing
   */
  trackApiPerformance(endpoint, method) {
    const startTime = performance.now();

    return {
      finish: (statusCode, error = null) => {
        try {
          const duration = (performance.now() - startTime) / 1000; // Convert to seconds
          const status = error ? 'error' : 'success';

          // Track response time
          this.metricsService.trackResponseTime('api_request', status, duration);

          // Track API requests
          this.metricsService.trackApiRequest(endpoint, method, statusCode.toString());

          // Track errors if any
          if (error) {
            this.metricsService.trackError('api_error', 'warning', 'api');
          }

          Logger.debug(`API Performance: ${method} ${endpoint} - ${duration.toFixed(3)}s - ${statusCode}`);
        } catch (trackingError) {
          Logger.error('Failed to track API performance:', trackingError);
        }
      }
    };
  }

  /**
   * Track database operation performance
   */
  trackDatabaseOperation(operationType) {
    const startTime = performance.now();

    return {
      finish: (success = true, error = null) => {
        try {
          const duration = (performance.now() - startTime) / 1000;
          const status = success ? 'success' : 'error';

          this.metricsService.trackResponseTime('database_query', status, duration);

          if (error) {
            this.metricsService.trackError('database_error', 'error', 'database');
          }

          Logger.debug(`DB Operation: ${operationType} - ${duration.toFixed(3)}s - ${status}`);
        } catch (trackingError) {
          Logger.error('Failed to track database operation:', trackingError);
        }
      }
    };
  }

  /**
   * Track Redis cache operations
   */
  trackCacheOperation(operationType, success = true) {
    try {
      const status = success ? 'success' : 'error';
      this.metricsService.trackRedisOperation(operationType, status);

      if (!success) {
        this.metricsService.trackError('cache_error', 'warning', 'redis');
      }

      Logger.debug(`Cache Operation: ${operationType} - ${status}`);
    } catch (error) {
      Logger.error('Failed to track cache operation:', error);
    }
  }

  /**
   * Track command execution performance
   */
  trackCommandExecution(commandName, userId, executionTime, success = true) {
    try {
      const status = success ? 'success' : 'error';

      // Track response time
      this.metricsService.trackResponseTime('command_execution', status, executionTime);

      // Track feature usage
      this.metricsService.trackFeatureUsage(`command_${commandName}`, 'user');

      // Track user activity
      this.trackUserActivity(userId, 'command');

      if (!success) {
        this.metricsService.trackError('command_error', 'warning', 'commands');
      }

      Logger.debug(`Command Execution: ${commandName} - ${executionTime.toFixed(3)}s - ${status}`);
    } catch (error) {
      Logger.error('Failed to track command execution:', error);
    }
  }

  /**
   * Generate periodic business metrics
   * This should be called by a scheduled task
   */
  async generatePeriodicMetrics() {
    try {
      const db = (await import('#src/utils/Db.js')).default;

      // Calculate user engagement metrics
      const now = new Date();
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      // Real database queries for user engagement
      const [activeUsers1h, activeUsers24h, newUsers24h, totalHubs, totalConnections] = await Promise.all([
        // Active users in last hour (based on message activity)
        db.message.groupBy({
          by: ['authorId'],
          where: { createdAt: { gte: hourAgo } },
          _count: true
        }).then(result => result.length),

        // Active users in last 24 hours
        db.message.groupBy({
          by: ['authorId'],
          where: { createdAt: { gte: dayAgo } },
          _count: true
        }).then(result => result.length),

        // New users in last 24 hours (first message)
        db.user.count({
          where: {
            createdAt: { gte: dayAgo }
          }
        }),

        // Total active hubs
        db.hub.count(),

        // Total active connections
        db.connection.count({
          where: { connected: true }
        })
      ]);

      // Track user engagement metrics
      this.metricsService.trackUserEngagement('active_users', '1h', activeUsers1h);
      this.metricsService.trackUserEngagement('active_users', '24h', activeUsers24h);
      this.metricsService.trackUserEngagement('new_users', '24h', newUsers24h);

      // Track hub ecosystem metrics
      this.metricsService.trackUserEngagement('total_hubs', 'current', totalHubs);
      this.metricsService.trackUserEngagement('total_connections', 'current', totalConnections);

      // Get top active hubs
      const topHubs = await db.hub.findMany({
        select: {
          id: true,
          name: true,
          messageCount: true,
          _count: {
            select: { connections: true }
          }
        },
        orderBy: { messageCount: 'desc' },
        take: 10
      });

      // Track individual hub metrics
      for (const hub of topHubs) {
        this.metricsService.trackHubActivity(hub.id, hub.name, 'message_count', hub.messageCount);
        this.metricsService.trackHubActivity(hub.id, hub.name, 'connection_count', hub._count.connections);

        // Calculate hub health score (0-1 based on activity and connections)
        const healthScore = Math.min(1, (hub.messageCount / 1000) * (hub._count.connections / 5));
        this.metricsService.trackHubActivity(hub.id, hub.name, 'health_score', healthScore);
      }

      Logger.info(`Generated periodic business metrics: ${activeUsers1h} active users (1h), ${activeUsers24h} active users (24h), ${newUsers24h} new users`);
    } catch (error) {
      Logger.error('Failed to generate periodic metrics:', error);
    }
  }
}

/**
 * Middleware for automatic API tracking
 */
export function createMetricsMiddleware(metricsService) {
  const tracker = new MetricsTracker(metricsService);

  return (req, res, next) => {
    const tracking = tracker.trackApiPerformance(req.path, req.method);

    // Override res.end to capture response
    const originalEnd = res.end;
    res.end = function(...args) {
      tracking.finish(res.statusCode, res.statusCode >= 400 ? new Error(`HTTP ${res.statusCode}`) : null);
      originalEnd.apply(this, args);
    };

    next();
  };
}

export default MetricsTracker;
