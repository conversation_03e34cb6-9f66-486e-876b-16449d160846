I want to implement a comprehensive premium subscription system for my interchat application with the following requirements:

**Core Premium Features:**
1. Premium users can send images during calls
2. Premium users can purchase enhanced features for their hubs

**Hub Subscription Tiers:**
- Design a tiered subscription system for hubs with appropriate names, pricing, and features
- Free tier: Maximum 50 server connections
- Paid tiers: Should include higher connection limits (e.g., 125+ connections)
- Allow users to purchase additional connection slots individually (suggested price: $1.50 per 50 slots as example)

**Requirements for Implementation:**
1. Analyze the current codebase to understand the existing user and hub architecture
2. Design 3-4 subscription tiers with:
   - Appropriate tier names (professional/creative naming)
   - Realistic pricing structure
   - Clear feature differentiation
   - Connection slot limits for each tier
3. Plan the database schema changes needed for:
   - User premium status tracking
   - Hub subscription management
   - Connection slot tracking and limits
4. Design the backend API endpoints for:
   - Subscription management
   - Feature access validation
   - Payment processing integration points
5. Plan frontend UI changes for:
   - Subscription management interface
   - Premium feature indicators
   - Upgrade prompts and billing

**Deliverables:**
- Detailed subscription tier structure with names, prices, and features
- Database schema modifications
- API endpoint specifications
- Implementation plan with file-by-file changes needed
- Consider integration points for payment processing (Ko-fi)

Please first analyze the current codebase structure, then propose the subscription tiers and create a comprehensive implementation plan. Please stick within the range of 1-15$ as this is a subscription for users on discord, who wouldnt pay huge amounts.
