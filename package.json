{"name": "interchat", "private": true, "version": "5.0.0", "description": "A growing Discord bot which provides inter-server chat!", "main": "build/index.js", "license": "AGPL-3.0-only", "scripts": {"start": "node .", "start:prod": "pm2 start .ecosystem.config.js", "dev": "nodemon --exec \"bun run build && bun start\" --ext ts,js,json --ignore build/", "build": "tsc --build", "typecheck": "tsc --noEmit", "locale-types": "bun scripts/genLocaleTypes.js", "sync:commands": "bun scripts/syncCommands.js", "sync:emojis": "bun scripts/syncEmojis.js", "migrate:nsfw-connections": "bun scripts/migrate-nsfw-connections.ts", "migrate:media-to-redis": "bun scripts/migrate-media-to-redis.ts", "release": "release-it", "lint": "eslint --cache --fix ./src", "prepare": "husky"}, "sponsor": {"url": "https://ko-fi.com/interchat"}, "type": "module", "dependencies": {"@hono/node-server": "^1.14.4", "@hono/zod-validator": "^0.7.0", "@prisma/client": "^6.10.0", "@sentry/node": "^9.30.0", "common-tags": "^1.8.2", "discord-hybrid-sharding": "^2.2.6", "discord.js": "^14.20.0", "dotenv": "^16.5.0", "hono": "^4.8.0", "husky": "^9.1.7", "ioredis": "^5.6.1", "js-yaml": "^4.1.0", "lodash": "^4.17.21", "lz-string": "^1.5.0", "ms": "^2.1.3", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.2", "zod": "^3.25.67"}, "devDependencies": {"@stylistic/eslint-plugin": "^4.4.1", "@types/common-tags": "^1.8.4", "@types/js-yaml": "^4.0.9", "@types/lodash": "^4.17.18", "@types/ms": "^2.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.29.0", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.5.3", "prisma": "^6.10.0", "release-it": "^19.0.3", "source-map-support": "^0.5.21", "typescript": "5.8.3", "typescript-eslint": "^8.34.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "lint-staged": {"*.ts": ["eslint --cache --fix"]}, "imports": {"#src/*.js": "./build/*.js", "#utils/*.js": "./build/utils/*.js"}}