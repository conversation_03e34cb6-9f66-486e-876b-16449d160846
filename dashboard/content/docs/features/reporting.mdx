---
title: Reporting
description: Report inappropriate content and users in InterChat hubs
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'

InterChat's reporting system allows users to flag inappropriate content or behavior to hub moderators for review.

## How Reporting Works

The reporting system creates a direct line of communication between users and hub moderators:

1. Users can report messages they find inappropriate
2. Reports are sent to the hub's designated report logging channel
3. Moderators can review reports and take appropriate action
4. The reporting user receives confirmation that their report was submitted

<Callout type="info">
  For the reporting system to work, hub owners must set up a report logging channel using `/hub config logging`.
</Callout>

## Reporting Messages

<Steps>
  ### Access the message context menu

  Right-click (or tap and hold on mobile) on the message you want to report.

  ### Select "Apps" then "Report Message"

  In the context menu, navigate to Apps > InterChat > Report Message.

  ### Choose a reason

  Select the most appropriate reason for your report from the dropdown menu:

  - Inappropriate content
  - Harassment
  - Spam
  - NSFW content
  - Etc.

  ### Submit the report

  After selecting a reason, your report will be submitted to the hub moderators.

  You'll receive a confirmation that your report was submitted successfully.
</Steps>

## For Moderators: Handling Reports

When a report is submitted, it appears in your configured report logging channel with:

- The reported message content
- An image attachment (if any)
- The user who sent the message
- The server the message came from
- The reason for the report
- Who submitted the report

### Available Actions

As a moderator, you have several options for handling reports:

1. **Take Action**: Opens the moderation panel with options to:
   - Delete the message
   - Blacklist the user
   - Blacklist the server
   - Warn the user - Issues a formal warning that's recorded in the user's infraction history
   - View user infractions

2. **Mark as Resolved**: Indicates the report has been handled

3. **Ignore Report**: Dismisses reports that don't require action

4. **Jump to Message**: Takes you directly to the reported message

<Callout type="warning">
  Deleted messages cannot be recovered. Make sure to review the content carefully before taking action.
</Callout>

## Setting Up Report Logging

To enable the reporting system for your hub:

1. Use the `/hub config logging` command
2. Select "Configure Report Logs" (`reports`) from the dropdown menu
3. Choose a channel where reports should be sent
4. Optionally, set a role to be pinged when reports are received

You can configure other logging types in a similar way:
- `modLogs` - For moderation actions
- `joinLeaves` - For servers joining or leaving the hub
- `appeals` - For blacklist appeal requests
- `networkAlerts` - For important system notifications

<Callout type="tip">
  Create a private channel specifically for reports to keep them organized and ensure only moderators can see them.
</Callout>

## Best Practices

For an effective reporting system:

- Respond to reports promptly
- Be consistent in how you handle similar violations
- Keep reporters anonymous to prevent retaliation
- Follow up on patterns of behavior, not just individual incidents
- Regularly review your hub rules to ensure they're clear about what content is reportable
- Consider having moderators in different time zones to ensure reports are handled quickly regardless of when they're submitted
