---
title: Hub Logging Configuration
description: Configure logging channels to monitor activity in your InterChat hub
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Steps } from 'fumadocs-ui/components/steps'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Hub Logging Configuration

Logging is an essential part of managing your InterChat hub effectively. By configuring logging channels, you can monitor various activities, receive notifications about important events, and maintain a record of moderation actions.

<Callout type="important" emoji="⚖️">
  This documentation is part of InterChat, which is licensed under the GNU Affero General Public License v3.0. Unauthorized copying, modification, or distribution is prohibited. See the license for more details.
</Callout>

## Understanding Hub Logs

Hub logs provide a way to track and monitor different types of activities within your hub. Each log type serves a specific purpose and can be configured to send notifications to a designated channel in your server.

### Available Log Types

InterChat supports five different types of logs:

1. **Moderation Logs** (`modLogs`)
   - Records all moderation actions taken within your hub
   - Includes blacklisting, warnings, and other administrative actions
   - Helps maintain accountability among your moderation team

2. **Join/Leave Logs** (`joinLeaves`)
   - Tracks servers joining or leaving your hub
   - Notifies when a server connects or disconnects a channel
   - Helps monitor hub growth and server participation

3. **Report Logs** (`reports`)
   - Receives notifications when content is reported by users
   - Includes the reported message, reporter information, and reason
   - Essential for addressing user concerns and maintaining community standards
   - Can be configured with a role mention to alert moderators

4. **Appeal Logs** (`appeals`)
   - Tracks blacklist appeal requests from users
   - Notifies when a user appeals a blacklist decision
   - Helps manage the appeal process efficiently
   - Can be configured with a role mention to alert moderators

5. **Network Alert Logs** (`networkAlerts`)
   - Receives anti-swear violations and actions taken
   - Can be configured with a role mention to alert administrators

<Callout type="info">
  Setting up report logging is required if you want to make your hub public. This ensures that user reports can be properly addressed by your moderation team.
</Callout>

## Configuring Hub Logs

You can configure logging channels and role mentions using the `/hub config logging` command.

<Steps>
  ### Access logging configuration

  Use the command `/hub config logging hub:YourHubName` to access the logging settings.

  ### Select a log type to configure

  From the dropdown menu, select the type of log you want to configure:
  - Moderation Logs (`modLogs`)
  - Join/Leave Logs (`joinLeaves`)
  - Report Logs (`reports`)
  - Appeal Logs (`appeals`)
  - Network Alert Logs (`networkAlerts`)
  - Message Moderation Logs (`messageModeration`)

  ### Choose a channel

  Select the channel where you want the logs to be sent. This should typically be a private channel that only moderators or administrators can access.

  ### Set up role mentions (for applicable log types)

  For `reports`, `appeals`, and `networkAlerts`, you can optionally select a role to be pinged when logs are sent. This ensures that your moderation team is promptly notified of important events.

  ### Confirm your settings

  After selecting a channel (and optionally a role), your configuration will be saved automatically. You'll receive a confirmation message indicating that the log type has been successfully configured.
</Steps>

## Managing Log Configurations

### Viewing Current Log Settings

To view your current logging configuration:

1. Use the `/hub config logging hub:YourHubName` command
2. The response will show all configured log types, their channels, and any associated role mentions

### Removing a Log Configuration

To remove a log configuration:

1. Use the `/hub config logging hub:YourHubName` command
2. Select the log type you want to reset
3. When prompted to select a channel, don't select any channel and submit
4. The log configuration will be reset, and you'll receive a confirmation message

### Updating a Log Configuration

To update an existing log configuration:

1. Use the `/hub config logging hub:YourHubName` command
2. Select the log type you want to update
3. Choose a new channel and/or role
4. Your configuration will be updated automatically

## Best Practices for Hub Logging

For effective hub management through logging:

- **Use private channels**: Create dedicated private channels for logs to ensure only authorized users can access them
- **Set up all log types**: Configure all available log types to maintain comprehensive oversight of your hub
- **Assign appropriate roles**: For log types that support role mentions, create specific roles for different responsibilities
- **Regular review**: Periodically review logs to identify patterns and address recurring issues
- **Clear naming conventions**: Use clear channel names like `#hub-mod-logs` or `#hub-reports` to easily identify log channels

<Callout type="tip">
  Consider creating a dedicated category or forum in your server for all hub-related log channels to keep them organized and easily accessible to your moderation team.
</Callout>

## Troubleshooting

If you encounter issues with hub logging:

- **Logs not appearing**: Ensure the bot has proper permissions in the configured channel
- **Role mentions not working**: Verify that the role is mentionable and properly configured
- **Configuration not saving**: Make sure you have the required permissions (Hub Owner or Manager)

<Callout type="warning">
  If you delete a channel that was configured for logging, you'll need to set up a new logging channel for that log type.
</Callout>
