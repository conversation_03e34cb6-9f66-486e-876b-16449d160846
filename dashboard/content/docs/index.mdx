---
title: Welcome to InterChat
description: Connect Discord communities seamlessly with real-time cross-server communication. Build bridges between servers and create vibrant, engaged communities.
icon: House
---

import { Cards, Card } from 'fumadocs-ui/components/card'
import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { LinkIcon, HouseIcon, ShieldIcon, ZapIcon, BookOpen, Rocket, Users, Settings } from 'lucide-react'

<div className="flex justify-center mb-8">
  <img src="/InterChatLogo.png" alt="InterChat Logo" width="150" height="150" className="rounded-full" />
</div>

# Transform Your Discord Experience

InterChat breaks down the walls between Discord communities, enabling **real-time cross-server communication** that brings people together across server boundaries. Whether you're building topic-focused communities, connecting with like-minded servers, or expanding your reach, InterChat makes it effortless.

<Callout type="info">
  **New to InterChat?** Start with our [Quick Start Guide](/docs/getting-started) to be chatting across servers in under 5 minutes!
</Callout>

## What You Can Do

<Cards>
  <Card title="Connect Communities" href="/docs/getting-started" icon={<LinkIcon className='text-blue-500' />}>
    Link Discord servers together for seamless cross-server conversations
  </Card>
  <Card title="Build Hubs" href="/docs/guides/creating-hubs" icon={<HouseIcon className='text-green-500' />}>
    Create themed communities that bring multiple servers together
  </Card>
  <Card title="Moderate Safely" href="/docs/guides/moderation" icon={<ShieldIcon className='text-red-500' />}>
    Keep your communities safe with powerful moderation tools
  </Card>
  <Card title="Customize Everything" href="/docs/guides/hub-management" icon={<Settings className='text-purple-500' />}>
    Tailor your hub experience with extensive customization options
  </Card>
</Cards>

## Choose Your Path

<Cards>
  <Card title="🚀 Quick Start" href="/docs/getting-started" icon={<Rocket className='text-orange-500' />}>
    **New to InterChat?** Get up and running in minutes with our step-by-step guide.
  </Card>
  <Card title="📚 Complete Guides" href="/docs/guides" icon={<BookOpen className='text-blue-500' />}>
    **Ready to dive deep?** Comprehensive tutorials for every feature and use case.
  </Card>
  <Card title="👥 Join Community" href="https://discord.gg/cgYgC6YZyX" icon={<Users className='text-green-500' />}>
    **Need help?** Connect with our community and get support from experts.
  </Card>
</Cards>

## Popular Use Cases

### 🎮 Gaming Communities
Connect gaming servers for tournaments, events, and cross-server gameplay coordination.

### 📚 Educational Networks
Link study groups, academic servers, and educational communities for knowledge sharing.

### 🎨 Creative Collaborations
Bring together artists, writers, and creators for collaborative projects and feedback.

### 💼 Professional Networks
Connect business communities, developer groups, and professional organizations.

<Callout type="warning">
  **License Notice:** InterChat is open-source under GNU AGPL-3.0. Respect our [license terms](/docs/legal/license-compliance) when using or modifying the code.
</Callout>

---

**Ready to get started?** [Add InterChat to your server](https://interchat.tech/invite) and begin connecting communities today!
