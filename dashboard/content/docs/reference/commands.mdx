---
title: Complete Command Reference
description: Comprehensive reference for all InterChat commands with syntax, examples, and permission requirements.
icon: Terminal
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Complete Command Reference

This is the definitive reference for all InterChat commands. Commands are organized by category with complete syntax, examples, and permission requirements.

<Callout type="info">
  **New to commands?** Start with our [Essential Commands Guide](/docs/guides/commands) for the most commonly used commands with practical examples.
</Callout>

## Command Syntax

InterChat supports both slash commands and prefix commands:

**Slash Commands (Recommended):**
```
/command option1:value1 option2:value2
```

**Prefix Commands:**
```
i.command
```

## Permission Levels

| Level | Description | Commands Available |
|-------|-------------|-------------------|
| **Everyone** | Any Discord user | Basic info, browsing, reporting |
| **Server Admin** | Manage Server permission | Connection management, hub creation |
| **Hub Moderator** | Hub moderation role | Warnings, blacklists, infractions |
| **Hub Manager** | Hub management role | Most hub settings, moderator management |
| **Hub Owner** | Hub creator/owner | All hub commands, ownership transfer |

## Hub Commands

### Hub Creation & Management

#### `/hub create`
**Purpose:** Create a new InterChat hub
**Permissions:** Everyone
**Cooldown:** 10 minutes

**Usage:**
```
/hub create
```

**Process:**
1. Opens a modal form
2. Fill in hub name, description, and optional icon URL
3. Submit to create your hub
4. You become the hub owner

**Example Form Data:**
- **Name:** "Indie Game Developers"
- **Description:** "A community for independent game developers to share resources, get feedback, and collaborate on projects."
- **Icon URL:** "https://example.com/icon.png" (optional)

---

#### `/hub edit`
**Purpose:** Edit hub details (name, description, icon)
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub edit hub:HubName
```

**Options:**
- `hub` - The name of the hub to edit

---

#### `/hub delete`
**Purpose:** Permanently delete a hub
**Permissions:** Hub Owner

**Usage:**
```
/hub delete hub:HubName
```

<Callout type="warning">
  **Warning:** This action is permanent and cannot be undone. All connections will be removed.
</Callout>

---

#### `/hub transfer`
**Purpose:** Transfer hub ownership to another user
**Permissions:** Hub Owner

**Usage:**
```
/hub transfer hub:HubName user:@NewOwner
```

**Options:**
- `hub` - The name of the hub
- `user` - The user to transfer ownership to

---

#### `/hub visibility`
**Purpose:** Change hub visibility (public/private)
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub visibility hub:HubName visibility:public
/hub visibility hub:HubName visibility:private
```

**Requirements for Public Hubs:**
- Hub must be 24+ hours old
- Must have 2+ moderators
- Must have report logging configured

---

#### `/hub servers`
**Purpose:** List all servers connected to a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub servers hub:HubName
```

Shows server names, connection times, and activity levels.

### Hub Discovery & Joining

#### **Hub Directory Website**
**Purpose:** Browse and discover public hubs
**Access:** Everyone

**How to Access:**
Visit [interchat.tech/hubs](https://interchat.tech/hubs) directly in your browser.

**Features:**
- Browse public hubs by category
- Search for specific topics and interests
- View ratings, reviews, and member counts
- Filter by activity level and community size
- Join hubs directly from the website

---

#### `/hub join`
**Purpose:** Join a hub using an invitation code
**Permissions:** Everyone

**Usage:**
```
/hub join invite:abc123def
```

**Options:**
- `invite` - The invitation code provided by the hub owner

### Hub Configuration

#### `/hub config rules`
**Purpose:** Set or update hub rules
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config rules hub:HubName
```

Opens a modal where you can enter hub rules that will be shown to users when they join.

---

#### `/hub config welcome`
**Purpose:** Set welcome message for new servers
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config welcome hub:HubName
```

**Available Variables:**
- `{hubName}` - Hub's name
- `{serverName}` - Joining server's name
- `{user}` - User who connected the channel
- `{memberCount}` - Server member count
- `{totalConnections}` - Total connected servers

---

#### `/hub config settings`
**Purpose:** Configure general hub settings
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config settings hub:HubName
```

**Available Settings:**
- **Block Invites** - Prevents Discord invite links
- **Use Nicknames** - Shows server nicknames instead of usernames
- **Block NSFW** - Filters inappropriate images
- **Hide Links** - Prevents links from being sent
- **Spam Filter** - Enables spam detection
- **Reactions** - Allows emoji reactions across servers

---

#### `/hub config logging`
**Purpose:** Configure activity logging channels
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config logging hub:HubName
```

**Log Types:**
- **Mod Logs** (`modLogs`) - Record moderation actions
- **Join/Leaves** (`joinLeaves`) - Track server connections
- **Reports** (`reports`) - User-submitted content reports
- **Appeals** (`appeals`) - Blacklist appeal requests
- **Network Alerts** (`networkAlerts`) - System notifications

---

#### `/hub config anti-swear`
**Purpose:** Configure content filtering rules
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub config anti-swear hub:HubName
```

**Pattern Types:**
- `word` - Exact word match
- `word*` - Prefix matching
- `*word` - Suffix matching
- `*word*` - Contains matching

**Actions:**
- **Block** - Prevent message from being sent
- **Blacklist** - Automatically blacklist sender (10 minutes)
- **Send Alert** - Notify moderators

### Hub Moderation

#### `/hub moderator add`
**Purpose:** Add a moderator to the hub
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub moderator add hub:HubName user:@User position:moderator
/hub moderator add hub:HubName user:@User position:manager
```

**Positions:**
- `moderator` - Basic moderation permissions
- `manager` - Advanced management permissions

---

#### `/hub moderator remove`
**Purpose:** Remove a moderator from the hub
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub moderator remove hub:HubName user:@User
```

---

#### `/hub moderator list`
**Purpose:** List all hub moderators
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub moderator list hub:HubName
```

Shows all moderators with their roles and permissions.

### Hub Communication

#### `/hub announce`
**Purpose:** Send announcement to all connected channels
**Permissions:** Hub Owner, Hub Manager, Hub Moderator
**Cooldown:** 1 minute

**Usage:**
```
/hub announce hub:HubName
```

Opens a modal for entering your announcement (up to 4000 characters).

### Hub Invitations

#### `/hub invite create`
**Purpose:** Create invitation codes for private hubs
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite create hub:HubName expiry:7d
/hub invite create hub:HubName uses:10 expiry:24h
```

**Options:**
- `expiry` - How long the invite is valid (1h, 24h, 7d, 30d)
- `uses` - Maximum number of uses (optional)

---

#### `/hub invite list`
**Purpose:** List all active invitations
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite list hub:HubName
```

---

#### `/hub invite revoke`
**Purpose:** Revoke an existing invitation
**Permissions:** Hub Owner, Hub Manager

**Usage:**
```
/hub invite revoke hub:HubName code:abc123def
```

### Hub Information

#### `/hub infractions`
**Purpose:** View infractions in a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/hub infractions hub:HubName
/hub infractions hub:HubName user:123456789012345678
```

**Options:**
- `user` - View infractions for specific user (optional)

Shows blacklists, warnings, and their status.

## Connection Commands

### Basic Connection Management

#### `/connect`
**Purpose:** Connect current channel to a hub
**Permissions:** Server Admin
**Cooldown:** 30 seconds

**Usage:**
```
/connect hub:HubName
```

**Requirements:**
- Bot must have "Manage Webhooks" permission
- Channel must not already be connected to a hub
- Server must not already be connected to this hub

---

#### `/disconnect`
**Purpose:** Disconnect current channel from its hub
**Permissions:** Server Admin

**Usage:**
```
/disconnect
```

<Callout type="warning">
  **Warning:** This action is permanent. You'll need to create a new connection to rejoin the hub.
</Callout>

### Connection Monitoring

#### `/connection list`
**Purpose:** List all connections in the server
**Permissions:** Server Admin

**Usage:**
```
/connection list
```

Shows:
- Connected channels and their hubs
- Connection status (active/paused)
- Last activity timestamps

---

#### `/connection pause`
**Purpose:** Temporarily pause a connection
**Permissions:** Server Admin

**Usage:**
```
/connection pause channel:#channel
/connection pause
```

**Options:**
- `channel` - Specific channel to pause (defaults to current channel)

---

#### `/connection unpause`
**Purpose:** Resume a paused connection
**Permissions:** Server Admin

**Usage:**
```
/connection unpause channel:#channel
/connection unpause
```

---

#### `/connection edit`
**Purpose:** Modify connection settings
**Permissions:** Server Admin

**Usage:**
```
/connection edit channel:#channel
/connection edit
```

**Available Options:**
- Change connected channel
- Toggle compact mode
- Update webhook settings

## Moderation Commands

### User Moderation

#### `/warn`
**Purpose:** Issue a formal warning to a user
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/warn user:@Username hub:HubName reason:"Please keep discussions on-topic"
```

**Options:**
- `user` - The user to warn
- `hub` - The hub where the warning applies
- `reason` - Explanation for the warning

---

#### `/blacklist user`
**Purpose:** Blacklist a user from a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist user hub:HubName user:@User reason:"Repeated spam" duration:7d
/blacklist user hub:HubName user:123456789012345678 reason:"Harassment"
```

**Options:**
- `hub` - The hub name
- `user` - User ID or mention
- `reason` - Reason for blacklisting
- `duration` - Optional duration (1h, 1d, 7d, 30d, etc.)

---

#### `/blacklist server`
**Purpose:** Blacklist an entire server from a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist server hub:HubName server:123456789012345678 reason:"Multiple violations"
```

**Options:**
- `hub` - The hub name
- `server` - Server ID
- `reason` - Reason for blacklisting
- `duration` - Optional duration

---

#### `/unblacklist user`
**Purpose:** Remove user from blacklist
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/unblacklist user hub:HubName user:@User
```

---

#### `/unblacklist server`
**Purpose:** Remove server from blacklist
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/unblacklist server hub:HubName server:123456789012345678
```

### Blacklist Management

#### `/blacklist list`
**Purpose:** View all active blacklists in a hub
**Permissions:** Hub Owner, Hub Manager, Hub Moderator

**Usage:**
```
/blacklist list hub:HubName
```

Shows all blacklisted users and servers with reasons and expiry times.

## Utility Commands

#### `/help`
**Purpose:** Show all available commands
**Permissions:** Everyone

**Usage:**
```
/help
```

Displays a list of all commands available to your permission level.

---

#### `/stats`
**Purpose:** Show bot statistics
**Permissions:** Everyone

**Usage:**
```
/stats
```

Displays information about bot uptime, connected servers, and active hubs.

---

#### `/call`
**Purpose:** Start a random text-chat with another server
**Permissions:** Everyone
**Cooldown:** 5 minutes

**Usage:**
```
/call
```

**What it does:**
- Connects you with a random server for chat
- Creates a temporary connection between both servers
- Allows cross-server communication
- Includes rating and reporting features

**Note:** Calls are in **beta**. For more active & reliable communication, consider using hubs.

---

#### `/leaderboard`
**Purpose:** View various community leaderboards
**Permissions:** Everyone

**Usage:**
```
/leaderboard messages
/leaderboard calls
/leaderboard votes
/leaderboard achievements
```

**Leaderboard Types:**
- **Messages** - Top users and servers by message count
- **Calls** - Most active call participants
- **Votes** - Top voters for InterChat on bot lists
- **Achievements** - Users with the most achievements unlocked

---

#### `/achievements`
**Purpose:** View your or another user's achievements
**Permissions:** Everyone

**Usage:**
```
/achievements
/achievements user:@SomeUser
/achievements view:unlocked
/achievements view:locked
```

**Options:**
- `user` - View another user's achievements (optional)
- `view` - Filter by achievement status (all/unlocked/locked)

**Achievement Categories:**
- Communication milestones
- Community participation
- Special events and activities
- Hidden achievements for discovery

## Context Menu Commands

### Message Actions

**Report Message** (Everyone)
- Right-click message → Apps → InterChat → Report Message
- Select reason for report
- Sends report to hub moderators

**Mod Panel** (Moderators)
- Right-click message → Apps → InterChat → Mod Panel
- Quick access to moderation actions:
  - Delete message
  - Warn user
  - Blacklist user
  - Blacklist server
  - View user infractions
  - Remove all reactions

## Command Cooldowns

| Command | Cooldown | Scope |
|---------|----------|-------|
| `/hub create` | 10 minutes | Per user |
| `/hub announce` | 1 minute | Per user |
| `/connect` | 30 seconds | Per user |
| `/warn` | 5 seconds | Per user |
| `/blacklist` | 10 seconds | Per user |

## Error Handling

**Common Error Messages:**

- **"Missing Permissions"** - You don't have the required role or Discord permissions
- **"Hub Not Found"** - The hub name doesn't exist or is misspelled
- **"Already Connected"** - Channel is already connected to a hub
- **"Cooldown Active"** - You must wait before using this command again
- **"Invalid Duration"** - Duration format is incorrect (use 1h, 1d, 7d, etc.)

**Troubleshooting Tips:**
1. Check your spelling of hub names
2. Verify you have the correct permissions
3. Ensure the bot has necessary Discord permissions
4. Wait for cooldowns to expire
5. Use `/help` to verify command syntax

---

**Need more help?** Check our [Essential Commands Guide](/docs/guides/commands) for practical examples and use cases!
