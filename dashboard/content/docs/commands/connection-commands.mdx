---
title: Connection Commands
description: Commands for managing channel connections to hubs
icon: link
---

import { Callout } from 'fumadocs-ui/components/callout'

# Connection Commands

These commands allow you to connect channels to hubs and manage existing connections.

## Basic Connection Commands

### `/connect`

Connects the current channel to a hub.

**Usage:** `/connect hub:HubName`

**Options:**
- `hub` - The name of the hub to connect to

**Process:**
1. Creates a webhook in the channel
2. Establishes a connection to the specified hub
3. Sends a confirmation message

**Bot Permissions:** Manage Webhooks permission in the server
**Cooldown:** 30 seconds

<Callout type="info">
  You can only connect a channel to one hub at a time.
</Callout>

---

### `/disconnect`

Disconnects the current channel from its hub.

**Usage:** `/disconnect`

**Process:**
1. Removes the connection from the hub
2. Deletes the webhook
3. Sends a confirmation message

**Bot Permissions:** Manage Webhooks permission in the server

<Callout type="warning">
  This action is permanent. You'll need to create a new connection if you want to rejoin the hub.
</Callout>

## Connection Management Commands

### `/connection list`

Lists all connections in the current server.

**Usage:** `/connection list`

**Process:**
1. Displays a list of all channels in the server that are connected to hubs
2. Shows which hub each channel is connected to
3. Indicates whether each connection is active or paused

**Bot Permissions:** Send Messages permission in the server

---

### `/connection pause`

Temporarily pauses a connection.

**Usage:** `/connection pause channel:#channel`

**Options:**
- `channel` - The channel to pause (defaults to current channel)

**Process:**
1. Temporarily stops the channel from sending and receiving messages
2. The connection remains in place but becomes inactive
3. Sends a confirmation message

**Bot Permissions:** Manage Webhooks permission in the server

<Callout type="info">
  Pausing is useful for temporary maintenance or when you want to temporarily stop communication without removing the connection entirely.
</Callout>

---

### `/connection unpause`

Resumes a paused connection.

**Usage:** `/connection unpause channel:#channel`

**Options:**
- `channel` - The channel to unpause (defaults to current channel)

**Process:**
1. Reactivates a paused connection
2. The channel will start sending and receiving messages again
3. Sends a confirmation message

**Bot Permissions:** Manage Webhooks permission in the server

---

### `/connection edit`

Edits an existing connection.

**Usage:** `/connection edit channel:#channel`

**Options:**
- `channel` - The channel to edit (defaults to current channel)

**Process:**
1. Displays a menu with options to:
   - Change the connected channel
   - Update webhook settings
   - Toggle compact mode (simplified message display)
2. Applies the selected changes
3. Sends a confirmation message

**Bot Permissions:** Manage Webhooks permission in the server

## Troubleshooting Connections

If you're experiencing issues with connections:

1. Check if the connection is paused with `/connection list`
2. Try editing the connection with `/connection edit` to refresh the webhook
3. Ensure the bot has the necessary permissions in your channel
4. If problems persist, try disconnecting with `/disconnect` and creating a new connection with `/connect`

<Callout type="tip">
  If messages aren't being delivered, it's often because the webhook was deleted or is invalid. Editing the connection can fix this by creating a new webhook.
</Callout>
