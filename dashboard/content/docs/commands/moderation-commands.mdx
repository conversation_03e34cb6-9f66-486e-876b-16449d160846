---
title: Moderation Commands
description: Commands for moderating InterChat hubs
---

import { Callout } from 'fumadocs-ui/components/callout'

# Moderation Commands

These commands help you moderate your InterChat hubs by managing blacklists, handling reports, and tracking infractions.

## Blacklist Commands

### `/blacklist user`

Blacklists a user from a hub.

**Usage:** `/blacklist user hub:HubName user:UserID reason:Reason duration:Duration`

**Options:**
- `hub` - The name of the hub
- `user` - The ID or mention of the user to blacklist
- `reason` - The reason for blacklisting
- `duration` - Optional duration (e.g., "7d" for 7 days)

**Process:**
1. Adds the user to the hub's blacklist
2. Prevents their messages from appearing in the hub
3. Logs the action to the hub's moderation logs

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

<Callout type="info">
  If no duration is specified, the blacklist is permanent until manually removed.
</Callout>

---

### `/blacklist server`

Blacklists an entire server from a hub.

**Usage:** `/blacklist server hub:HubName server:ServerID reason:Reason duration:Duration`

**Options:**
- `hub` - The name of the hub
- `server` - The ID of the server to blacklist
- `reason` - The reason for blacklisting
- `duration` - Optional duration (e.g., "7d" for 7 days)

**Process:**
1. Adds the server to the hub's blacklist
2. Disconnects all channels from that server
3. Prevents the server from rejoining the hub
4. Logs the action to the hub's moderation logs

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

---

### `/blacklist list`

Lists all blacklisted users and servers in a hub.

**Usage:** `/blacklist list hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a list of all blacklisted users and servers
2. Shows who blacklisted them, when, and for what reason
3. Indicates when temporary blacklists will expire

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

## Unblacklist Commands

### `/unblacklist user`

Removes a user from a hub's blacklist.

**Usage:** `/unblacklist user hub:HubName user:UserID`

**Options:**
- `hub` - The name of the hub
- `user` - The ID or mention of the user to unblacklist

**Process:**
1. Removes the user from the hub's blacklist
2. Allows them to participate in the hub again
3. Logs the action to the hub's moderation logs

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

---

### `/unblacklist server`

Removes a server from a hub's blacklist.

**Usage:** `/unblacklist server hub:HubName server:ServerID`

**Options:**
- `hub` - The name of the hub
- `server` - The ID of the server to unblacklist

**Process:**
1. Removes the server from the hub's blacklist
2. Allows the server to join the hub again
3. Logs the action to the hub's moderation logs

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

## Report Handling

Reports are handled through buttons in the report logging channel rather than commands. When a report is received, moderators can:

1. **Take Action**: Opens a moderation panel with options to:
   - Delete the message
   - Blacklist the user
   - Blacklist the server
   - Warn the user
   - View user infractions

2. **Mark as Resolved**: Indicates the report has been handled

3. **Ignore Report**: Dismisses reports that don't require action

<Callout type="info">
  To enable reports, set up a report logging channel using `/hub config logging`.
</Callout>

## Warning Users

### `/warn`

Issues a warning to a user in a specific hub.

**Usage:** `/warn user:Username hub:HubName reason:Reason`

**Options:**
- `user` - The user to warn
- `hub` - The hub in which to issue the warning
- `reason` - The reason for the warning

**Process:**
1. Records the warning in the user's infraction history
2. Notifies the user they have been warned (if possible)
3. Logs the warning in the hub's moderation logs

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

---

## Infraction Management

### `/hub infractions`

Views all infractions in a hub.

**Usage:** `/hub infractions hub:HubName`

**Options:**
- `hub` - The name of the hub

**Process:**
1. Displays a list of all infractions in the hub
2. Includes blacklists and warnings
3. Shows active and expired/revoked infractions

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

## Context Menu Commands

In addition to slash commands, InterChat provides context menu commands for moderation:

### Report Message

**Access:** Right-click on a message > Apps > InterChat > Report Message

**Process:**
1. Select a reason for the report
2. The report is sent to the hub's report logging channel

**Permissions:** Everyone

### Mod Panel

**Access:** Right-click on a message > Apps > InterChat > Mod Panel

**Process:**
1. Opens a moderation panel with options to:
   - Delete the message
   - Blacklist the user
   - Blacklist the server
   - Warn the user
   - View user infractions

**Permissions:** Hub Owner, Hub Manager, Hub Moderator

<Callout type="tip">
  The Mod Panel is the quickest way to take moderation actions on specific messages.
</Callout>
