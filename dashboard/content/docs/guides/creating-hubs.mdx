---
title: Creating Your First Hub
description: Build a thriving cross-server community from the ground up. Learn how to create, configure, and grow a successful InterChat hub.
icon: Plus
---

import { Steps } from 'fumadocs-ui/components/steps'
import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

# Creating Your First Hub

A hub is your community's central meeting place where multiple Discord servers come together. This guide will help you create a hub that attracts members and fosters meaningful connections.

## What You'll Learn
- How to plan and create an effective hub
- Best practices for naming and describing your hub
- Essential configuration steps for new hubs
- Strategies for growing your community

## Before You Start

### Planning Your Hub

<Callout type="tip">
  **Success Tip:** The most successful hubs have a clear purpose and target audience. Spend time planning before creating!
</Callout>

Ask yourself these questions:
- **What's your hub's purpose?** (Gaming, learning, creative collaboration, etc.)
- **Who is your target audience?** (Specific game players, students, professionals, etc.)
- **What makes your hub unique?** (Special events, expert knowledge, unique community culture)
- **How will you moderate it?** (Rules, moderation team, content policies)

### Hub Examples for Inspiration

<Tabs items={['Gaming Hubs', 'Learning Hubs', 'Creative Hubs', 'Professional Hubs']}>
  <Tab>
    **Minecraft Builders Network**
    - Purpose: Share builds, collaborate on projects, get feedback
    - Audience: Minecraft builders of all skill levels
    - Unique Value: Weekly build challenges, expert tutorials

    **Indie Game Developers**
    - Purpose: Support indie developers, share resources, find collaborators
    - Audience: Independent game developers
    - Unique Value: Industry connections, feedback sessions
  </Tab>
  <Tab>
    **Computer Science Study Group**
    - Purpose: Help students with coursework, share resources
    - Audience: CS students and recent graduates
    - Unique Value: Peer tutoring, job search support

    **Language Exchange Network**
    - Purpose: Practice languages with native speakers
    - Audience: Language learners worldwide
    - Unique Value: Structured conversation practice
  </Tab>
  <Tab>
    **Digital Artists Collective**
    - Purpose: Share artwork, get critiques, collaborate
    - Audience: Digital artists and illustrators
    - Unique Value: Monthly art challenges, portfolio reviews

    **Writers Workshop**
    - Purpose: Share writing, get feedback, improve craft
    - Audience: Writers of all genres and experience levels
    - Unique Value: Writing prompts, critique exchanges
  </Tab>
  <Tab>
    **Web Developers Network**
    - Purpose: Share knowledge, discuss trends, find opportunities
    - Audience: Web developers and designers
    - Unique Value: Code reviews, job postings, tech discussions

    **Marketing Professionals Hub**
    - Purpose: Share strategies, discuss campaigns, network
    - Audience: Marketing professionals and students
    - Unique Value: Case study discussions, industry insights
  </Tab>
</Tabs>

## Step 1: Create Your Hub

<Steps>
  ### Run the Creation Command

  In any channel where InterChat is present, type:
  ```
  /hub create
  ```

  ### Fill Out the Creation Form

  A modal will appear with these fields:

  **Hub Name** (Required)
  - Must be unique across all InterChat hubs
  - Choose something memorable and descriptive
  - Examples: "Minecraft Builders Network", "CS Study Group", "Indie Game Devs"

  **Description** (Required)
  - Explain what your hub is about in 1-2 sentences
  - Include your target audience and main purpose
  - Example: "A community for Minecraft builders to share creations, collaborate on projects, and learn new techniques together."

  **Icon URL** (Optional)
  - Add a custom icon to make your hub stand out
  - Use a direct image URL (ending in .png, .jpg, etc.)
  - Recommended size: 512x512 pixels

  ### Submit and Confirm

  Click "Submit" to create your hub. You'll receive a confirmation message and become the hub owner.
</Steps>

## Step 2: Essential Initial Configuration

<Callout type="warning">
  **Important:** New hubs are private by default. Complete these configuration steps before making your hub public.
</Callout>

### Set Up Hub Rules

Clear rules create a welcoming environment and set expectations:

```
/hub config rules hub:YourHubName
```

**Example Rules:**
```
🌟 Welcome to [Hub Name]! Please follow these guidelines:

1. **Be Respectful** - Treat all members with kindness and respect
2. **Stay On Topic** - Keep discussions relevant to [hub purpose]
3. **No Spam** - Avoid repetitive messages or excessive self-promotion
4. **Use Appropriate Language** - Keep content family-friendly
5. **Follow Discord ToS** - All Discord Terms of Service apply
6. **Ask for Help** - Don't hesitate to reach out to moderators

Violations may result in warnings or removal from the hub.
```

### Configure Basic Settings

```
/hub config settings hub:YourHubName
```

**Recommended Initial Settings:**
- ✅ **Spam Filter** - Prevents spam messages
- ✅ **Block NSFW** - Filters inappropriate content
- ✅ **Reactions** - Allows emoji reactions across servers
- ❌ **Block Invites** - Allow Discord invites (can enable later if needed)
- ❌ **Hide Links** - Allow links (can enable later if needed)

### Create a Welcome Message

Make new servers feel welcome with a custom greeting:

```
/hub config welcome hub:YourHubName
```

**Example Welcome Message:**
```
🎉 Welcome to {hubName}!

We're excited to have {serverName} join our community of {totalConnections} connected servers!

**Getting Started:**
• Read our rules with `/hub config rules`
• Introduce your server in the chat
• Check out our weekly events and challenges

**Need Help?**
• Tag a moderator for assistance
• Use `/help` for command information

Happy chatting! 💬
```

## Step 3: Set Up Moderation

### Add Your First Moderator

Even small hubs benefit from having multiple moderators:

```
/hub moderator add hub:YourHubName user:@FriendOrTrustedMember position:moderator
```

### Configure Logging

Set up logging to monitor your hub's activity:

```
/hub config logging hub:YourHubName
```

**Essential Log Types:**
- **Reports** - User-submitted reports of inappropriate content
- **Mod Logs** - Record of all moderation actions
- **Join/Leaves** - Track servers joining or leaving your hub

<Callout type="tip">
  **Pro Tip:** Create dedicated channels like `#hub-reports` and `#hub-logs` to keep moderation organized.
</Callout>

## Step 4: Connect Your First Channel

Test your hub by connecting a channel from your own server:

```
/connect hub:YourHubName
```

Send a test message to make sure everything is working correctly.

## Step 5: Invite Your First Servers

### Create Invitation Codes

Since your hub is private, you'll need to create invites:

```
/hub invite create hub:YourHubName expiry:7d
```

### Share Your Hub

Reach out to:
- **Partner servers** you already know
- **Community forums** related to your topic
- **Social media** where your target audience gathers
- **Friends** who run relevant Discord servers

**Invitation Message Template:**
```
Hi! I've created a new InterChat hub called "[Hub Name]" for [target audience].

It's designed to [hub purpose] and I think your server would be a great fit!

The hub allows real-time chat between connected servers while keeping each server's independence. Would you be interested in joining?

Invite code: [your-invite-code]
Use: /hub join invite:[your-invite-code]
```

## Growing Your Hub

### Making Your Hub Public

Once your hub is established (24+ hours old, 2+ moderators, report logging configured), you can make it public:

```
/hub visibility hub:YourHubName visibility:public
```

This will list your hub in the [public directory](https://interchat.tech/hubs) for discovery.

## Common Mistakes to Avoid

<Callout type="warning">
  **Avoid These Pitfalls:**

  - **Vague Purpose** - "General chat" hubs rarely succeed
  - **No Moderation** - Unmoderated hubs quickly become chaotic
  - **Inactive Owner** - Hub owners must actively participate
  - **Too Many Rules** - Keep rules simple and enforceable
  - **Ignoring Feedback** - Listen to your community members
</Callout>

## Next Steps

<Callout type="success">
  **Congratulations!** You've created your first hub. Here's what to focus on next:
</Callout>

1. **[Build a moderation team](/docs/guides/moderation-team)** - Add trusted moderators
2. **[Set up content filtering](/docs/guides/content-filtering)** - Automate content moderation
3. **[Configure announcements](/docs/guides/announcements)** - Communicate with your community
4. **[Monitor with logging](/docs/guides/logging)** - Stay aware of hub activity

---

**Need help with your hub?** Join our [support community](https://discord.gg/cgYgC6YZyX) to connect with experienced hub owners and get personalized advice!
