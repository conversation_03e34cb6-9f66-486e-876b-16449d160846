---
title: Calls, Leaderboards & Achievements
description: Beyond hubs and connections, InterChat offers additional features to enhance your community experience. This guide covers calls, leaderboards, and the achievement system.
icon: Trophy
---

import { Callout } from 'fumadocs-ui/components/callout'
import { Tabs, Tab } from 'fumadocs-ui/components/tabs'

## What You'll Learn
- How to use the call system
- Understanding community leaderboards
- Exploring the achievement system
- Best practices for community engagement
- How these features complement hubs

<Callout type="info">
  **Note:** While these features add fun to your InterChat experience, **hubs remain the primary and most reliable way** to connect communities. Consider these as supplementary features for additional engagement.
</Callout>

## One-to-One Calls (Beta)

### Understanding Calls

InterChat's call system allows you to have **conversations with random servers**, creating spontaneous connections across Discord.

**How Calls Work:**
1. **Random Matching** - You're connected with another server looking for a call
2. **Connection Creation** - Temporary connection is automatically created between both servers
3. **Cross-Server Chat** - Members can join and talk across servers
4. **Call Management** - Rate, report, or end calls as needed

### Using the Call System

**Starting a Call:**
```
/call
```

**What Happens Next:**
1. InterChat searches for another server wanting to call
2. When matched, temporary connections are created in both servers
3. Members from both servers can join the temporary connections
4. You can chat, play games, or just hang out together

**Call Features:**
- **Skip Server** - If the match isn't good, skip to find another
- **End Call** - Terminate the call when finished
- **Rate Call** - Give feedback (👍 Good Call / 👎 Poor Call)
- **Report Call** - Report inappropriate behavior

### Call Best Practices

<Tabs items={['Before Calling', 'During Calls', 'After Calls']}>
  <Tab>
    **Preparation:**
    - Ensure your server has active chat users
    - Set clear expectations with your community
    - Have moderators available if needed
    - Consider time zones for better matches

    **Community Guidelines:**
    - Establish chat rules for your server
    - Brief members on cross-server etiquette
    - Prepare conversation starters or activities
    - Have backup plans if calls don't go well
  </Tab>
  <Tab>
    **Good Call Etiquette:**
    - Be welcoming and friendly to the other server
    - Introduce your server and community
    - Respect different cultures and languages
    - Keep conversations appropriate and inclusive

    **Managing Issues:**
    - Use the skip function if there's a poor match
    - Report serious violations immediately
    - End calls that become problematic
    - Support your community members
  </Tab>
  <Tab>
    **Post-Call Actions:**
    - Rate the call honestly to improve matching
    - Discuss the experience with your community
    - Consider following up with good connections
    - Report any issues that occurred

    **Building Connections:**
    - Exchange server invites if both sides agree
    - Plan future activities or collaborations
    - Share the experience on social media
    - Encourage positive community interactions
  </Tab>
</Tabs>

### Why Choose Hubs Over Calls?

<Callout type="tip">
  **Hubs vs. Calls:** While calls are fun for spontaneous interactions, hubs provide a more reliable and feature-rich experience for building lasting community connections.
</Callout>

**Hubs Advantages:**
- **Persistent Connections** - Messages stay even when you're offline
- **Multiple Communities** - Join various themed hubs or create your own
- **Advanced Moderation** - Content filtering, anti-spam, and comprehensive safety tools
- **Rich Features** - Custom welcome messages, rules, announcements, and settings
- **Active Communities** - Thousands of servers already connected in focused topics

**When to Use Each:**
- **Use Calls for:** Spontaneous interactions, meeting new communities, casual hangouts
- **Use Hubs for:** Ongoing community building, topic-focused discussions, reliable communication

## Community Leaderboards

### Understanding Leaderboards

InterChat tracks various community activities and displays top performers in different categories. Leaderboards encourage engagement and recognize active community members.

**Available Leaderboards:**
```
/leaderboard messages    # Top message senders
/leaderboard calls       # Most active call participants
/leaderboard votes       # Top voters for InterChat
/leaderboard achievements # Users with most achievements
```

### Leaderboard Categories

<Tabs items={['Messages', 'Calls', 'Votes', 'Achievements']}>
  <Tab>
    **Message Leaderboard**

    **What it tracks:**
    - Total messages sent through InterChat hubs
    - Both user and server rankings
    - Monthly statistics

    **How to climb:**
    - Participate actively in hub discussions
    - Send quality, engaging messages
    - Join multiple hubs for more opportunities
    - Be consistent in your participation

    **Recognition:**
    - Top users and servers displayed
    - Monthly reset keeps competition fresh
    - Encourages ongoing community engagement
  </Tab>
  <Tab>
    **Call Leaderboard**

    **What it tracks:**
    - Number of calls participated in
    - Call duration and engagement
    - Both user and server rankings

    **How to climb:**
    - Participate in calls regularly
    - Have positive call experiences
    - Encourage your server to use calls
    - Rate calls positively when appropriate
  </Tab>
  <Tab>
    **Voting Leaderboard**

    **What it tracks:**
    - Votes for InterChat on bot listing sites
    - Support for the project's growth
    - Community contribution recognition

    **How to participate:**
    - Vote for InterChat on bot lists
    - Encourage your community to vote
    - Support the project's visibility
    - Help InterChat reach more communities

    **Impact:**
    - Helps InterChat grow and improve
    - Brings more servers to the network
    - Supports ongoing development
  </Tab>
  <Tab>
    **Achievement Leaderboard**

    **What it tracks:**
    - Total achievements unlocked
    - Rare and difficult achievements
    - Overall community engagement

    **How to climb:**
    - Explore all InterChat features
    - Help other users and communities
    - Discover hidden achievements

    **Recognition:**
    - Shows most dedicated users
    - Encourages feature exploration
    - Builds community expertise
  </Tab>
</Tabs>

### Using Leaderboards for Community Building

**For Server Administrators:**
- Share leaderboard achievements with your community
- Encourage participation in InterChat features
- Recognize top contributors from your server
- Use rankings to motivate engagement

**For Community Members:**
- Compete friendly with other users
- Discover new ways to engage with InterChat
- Connect with other active community members
- Celebrate achievements and milestones

## Achievement System

### Understanding Achievements

InterChat's achievement system rewards users for various activities and milestones, encouraging exploration of features and community engagement.

**Viewing Achievements:**
```
/achievements                    # View your achievements
/achievements user:@SomeUser     # View another user's achievements
/achievements view:unlocked      # Show only unlocked achievements
/achievements view:locked        # Show only locked achievements
```

### Achievement Categories

**Communication Achievements:**
- First message sent through InterChat
- Milestone message counts (100, 1000, 10000+ messages)
- Cross-server conversation participation
- Hub creation and management

**Community Achievements:**
- Joining multiple hubs
- Successful server connections
- Positive community interactions
- Helping other users

**Special Event Achievements:**
- Early adopter recognition
- Beta feature testing
- Community milestones

**Hidden Achievements:**
- Secret achievements for discovery
- Easter eggs and special activities
- Rare accomplishments
- Community secrets

### Achievement Strategies

<Tabs items={['Getting Started', 'Advanced Goals', 'Hidden Secrets']}>
  <Tab>
    **Beginner Achievements:**
    - Send your first InterChat message
    - Join your first hub
    - Create a server connection
    - Use basic commands

    **Easy Wins:**
    - Participate in hub discussions
    - Help answer questions in support
    - Vote for InterChat on bot lists
    - Invite InterChat to your server

    **Building Momentum:**
    - Join multiple hubs with different topics
    - Create your own hub
    - Become active in community discussions
    - Help moderate a hub
  </Tab>
  <Tab>
    **Long-term Goals:**
    - Reach high message count milestones
    - Build a successful, active hub
    - Become a recognized community leader
    - Help with InterChat development

    **Community Leadership:**
    - Moderate multiple hubs effectively
    - Organize community events
    - Mentor new users and hub owners
    - Contribute to InterChat's growth

    **Technical Achievements:**
    - Self-host InterChat successfully
    - Contribute to the codebase
    - Create community tools and resources
    - Help with translations
  </Tab>
  <Tab>
    **Discovery Tips:**
    - Explore all InterChat features thoroughly
    - Participate in special events and activities
    - Look for easter eggs in commands and responses
    - Connect with the development team

    **Secret Achievement Hints:**
    - Some achievements require specific timing
    - Others need collaboration with other users
    - A few are tied to special community milestones
    - Some unlock only during special events

    **Community Secrets:**
    - Join the support server for insider information
    - Follow InterChat's social media for hints
    - Participate in beta testing programs
    - Connect with achievement hunters
  </Tab>
</Tabs>

### Achievement Benefits

**Personal Recognition:**
- Show off your InterChat expertise
- Track your community engagement progress
- Unlock exclusive profile features
- Gain recognition from other users

**Community Building:**
- Encourage server participation
- Create friendly competition
- Recognize dedicated community members
- Build engagement and retention

## Best Practices for Engagement

### Balancing Features

**Primary Focus: Hubs**
- Use hubs as your main community building tool
- Establish reliable, ongoing connections
- Build topic-focused communities
- Invest in moderation and community management

**Supplementary Features:**
- Use calls for fun, spontaneous interactions
- Track progress with achievements and leaderboards
- Encourage community participation in all features
- Celebrate milestones and accomplishments

### Community Integration

**For Server Owners:**
- Introduce all InterChat features to your community
- Set up dedicated channels for different activities
- Encourage participation in leaderboards and achievements
- Balance feature use with your server's main purpose

**For Community Members:**
- Explore all features to enhance your experience
- Participate in friendly competition
- Help newcomers discover InterChat's capabilities
- Share achievements and celebrate others' success

## Getting Help and Support

**Feature-Specific Support:**
- **Calls:** Report issues immediately, use rating system
- **Leaderboards:** Contact support for ranking questions
- **Achievements:** Ask in support server for hints and help

<Callout type="success">
  **Ready to explore?** Start with `/achievements` to see your current progress, try `/call` for a fun interaction, and check `/leaderboard messages` to see the most active community members!
</Callout>

---

**Want to focus on building lasting communities?** Check out our [Hub Creation Guide](/docs/guides/creating-hubs) and [Hub Management](/docs/guides/hub-management) for comprehensive community building strategies!
