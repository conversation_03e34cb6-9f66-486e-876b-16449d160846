"use client";

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { LeaderboardResponse } from '@/lib/platform-stats';
import {
    Award,
    Crown,
    Loader2,
    Medal,
    TrendingUp,
    Trophy,
    Users
} from 'lucide-react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';

interface LeaderboardHub {
  id: string;
  name: string;
  iconUrl: string;
  bannerUrl?: string;
  newServersJoined: number;
  activitySpike: number; // percentage
  totalServers: number;
  rank: number;
  verified?: boolean;
  partnered?: boolean;
}

interface LeaderboardSectionProps {
  initialData?: LeaderboardResponse;
}

/**
 * 5️⃣ Leaderboard Section (Competition)
 * Top Growing Hubs This Week with podium style layout - now with real data
 */
export function LeaderboardSection({ initialData }: LeaderboardSectionProps) {
  // Use initial data from SSR instead of client-side fetch
  const hubs = initialData?.data || [];
  const isLoading = false; // No loading state needed with SSR
  const error = initialData?.error || null;
  const isFallback = initialData?.fallback || false;
  const [first, second, third, ...rest] = hubs;

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return { icon: Crown, color: 'text-yellow-400', bg: 'bg-yellow-500/20' };
      case 2: return { icon: Medal, color: 'text-gray-300', bg: 'bg-gray-500/20' };
      case 3: return { icon: Award, color: 'text-amber-600', bg: 'bg-amber-500/20' };
      default: return { icon: Trophy, color: 'text-blue-400', bg: 'bg-blue-500/20' };
    }
  };

  const PodiumCard = ({ hub, size }: { hub: LeaderboardHub; size: 'large' | 'medium' | 'small' }) => {
    const rankInfo = getRankIcon(hub.rank);
    const cardHeight = size === 'large' ? 'h-80' : size === 'medium' ? 'h-72' : 'h-64';
    const iconSize = size === 'large' ? 'w-16 h-16' : 'w-12 h-12';

    return (
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: hub.rank * 0.1 }}
        className={`${size === 'large' ? 'order-2' : size === 'medium' ? 'order-1' : 'order-3'}`}
      >
        <Card className={`${cardHeight} bg-gradient-to-br from-gray-800/80 to-gray-900/80 border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 hover:scale-105 relative overflow-hidden`}>
          {/* Rank Badge */}
          <div className="absolute top-4 left-4 z-10">
            <div className={`${rankInfo.bg} rounded-full p-2`}>
              <rankInfo.icon className={`${iconSize} ${rankInfo.color}`} />
            </div>
          </div>

          {/* Rank Number */}
          <div className="absolute top-4 right-4 z-10">
            <Badge className={`${rankInfo.bg} ${rankInfo.color} border-none text-lg font-bold px-3 py-1`}>
              #{hub.rank}
            </Badge>
          </div>

          <CardContent className="p-6 h-full flex flex-col justify-between">
            {/* Hub Info */}
            <div className="text-center mt-8">
              <div className={`${iconSize} mx-auto mb-4 rounded-xl bg-gray-700/50 flex items-center justify-center overflow-hidden`}>
                <Image
                  src={hub.iconUrl}
                  alt={`${hub.name} icon`}
                  width={size === 'large' ? 64 : 48}
                  height={size === 'large' ? 64 : 48}
                  className="rounded-lg"
                />
              </div>
              <h3 className={`font-bold text-white mb-2 ${size === 'large' ? 'text-xl' : 'text-lg'}`}>
                {hub.name}
              </h3>
            </div>

            {/* Metrics */}
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">New Servers:</span>
                <span className="text-green-400 font-semibold">+{hub.newServersJoined}</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Activity Spike:</span>
                <span className="text-blue-400 font-semibold">+{hub.activitySpike}%</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-400">Total Servers:</span>
                <span className="text-white font-semibold">{hub.totalServers}</span>
              </div>
            </div>

            {/* Action Button */}
            <Button
              asChild
              size="sm"
              className="w-full mt-4 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none"
            >
              <Link href={`/hubs/${hub.id}`}>
                View Hub
              </Link>
            </Button>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  const ListItem = ({ hub }: { hub: LeaderboardHub }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: hub.rank * 0.05 }}
    >
      <Card className="bg-gray-800/30 border-gray-700/50 hover:bg-gray-800/50 transition-all duration-300">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <Badge className="bg-gray-700/50 text-gray-300 border-none font-bold px-2 py-1">
                #{hub.rank}
              </Badge>
              <div className="w-10 h-10 rounded-lg bg-gray-700/50 flex items-center justify-center overflow-hidden">
                <Image
                  src={hub.iconUrl}
                  alt={`${hub.name} icon`}
                  width={32}
                  height={32}
                  className="rounded-md"
                />
              </div>
              <div>
                <h4 className="font-semibold text-white">{hub.name}</h4>
                <div className="flex items-center gap-4 text-xs text-gray-400">
                  <span className="flex items-center gap-1">
                    <Users className="w-3 h-3" />
                    +{hub.newServersJoined} servers
                  </span>
                  <span className="flex items-center gap-1">
                    <TrendingUp className="w-3 h-3" />
                    +{hub.activitySpike}%
                  </span>
                </div>
              </div>
            </div>
            <Button asChild size="sm" variant="outline" className="ml-auto border-gray-600 hover:bg-gray-700/50">
              <Link href={`/hubs/${hub.id}`}>
                View
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="bg-gray-950 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-400" />
            <p className="text-gray-400">Loading leaderboard...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error && !isFallback) {
    return (
      <div className="bg-gray-950 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <Trophy className="w-8 h-8 mx-auto mb-4 text-gray-600" />
            <p className="text-gray-400">Unable to load leaderboard data</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-950 py-16">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl font-bold text-white mb-4 flex items-center justify-center gap-3">
            <Trophy className="w-8 h-8 text-yellow-400" />
            Top Growing Hubs This Week
            {isFallback && (
              <Badge className="bg-orange-500/20 text-orange-300 border-orange-500/30 text-xs">
                Demo Data
              </Badge>
            )}
          </h2>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Discover the fastest-growing communities with the highest engagement and new member activity
          </p>
        </motion.div>

        {/* Podium Style (Top 3) */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
          {first && <PodiumCard hub={first} size="large" />}
          {second && <PodiumCard hub={second} size="medium" />}
          {third && <PodiumCard hub={third} size="small" />}
        </div>

        {/* Rest listed below */}
        {rest.length > 0 && (
          <div className="max-w-3xl mx-auto">
            <h3 className="text-xl font-semibold text-white mb-6 text-center">Other Top Performers</h3>
            <div className="space-y-3">
              {rest.map((hub) => (
                <ListItem key={hub.id} hub={hub} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
