"use client";

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import type { SimplifiedHub } from '@/hooks/use-infinite-hubs';
import {
    Activity,
    Eye,
    Flame,
    Heart,
    Star,
    TrendingUp,
    Users
} from 'lucide-react';
import { motion } from 'motion/react';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

interface EnhancedHubCardProps {
  hub: SimplifiedHub;
  index?: number;
}

/**
 * 🔹 Enhanced Hub Card Design
 * Features banner image, improved layout, tags, server count, activity metrics, ratings, and hover effects
 */
export function EnhancedHubCard({ hub, index = 0 }: EnhancedHubCardProps) {
  const [imageError, setImageError] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  const serverCount = hub._count?.connections || hub.connections.length;
  const upvoteCount = hub.upvotes.length;
  const dailyActivity = hub._count.messages || 0;

  // Calculate rating based on upvotes (more realistic approach)
  // Convert upvotes to a 1-5 star rating scale
  const rating = upvoteCount > 0 ? Math.min(5, Math.max(1, 1 + (upvoteCount / 10))) : null;

  const getActivityColor = (level: string) => {
    switch (level) {
      case 'HIGH': return 'text-green-400 bg-green-500/10';
      case 'MEDIUM': return 'text-yellow-400 bg-yellow-500/10';
      case 'LOW': return 'text-gray-400 bg-gray-500/10';
      default: return 'text-gray-400 bg-gray-500/10';
    }
  };

  const getBadges = () => {
    const badges = [];
    if (hub.verified) badges.push({ label: '⭐ Verified', color: 'bg-blue-500/20 text-blue-300' });
    if (hub.partnered) badges.push({ label: '👑 Partnered', color: 'bg-purple-500/20 text-purple-300' });
    if (serverCount > 50) badges.push({ label: '🔥 Trending', color: 'bg-orange-500/20 text-orange-300' });
    if (dailyActivity > 100) badges.push({ label: '📈 Growing Fast', color: 'bg-green-500/20 text-green-300' });
    if (rating && rating >= 4.5) badges.push({ label: '⭐ Top Rated', color: 'bg-yellow-500/20 text-yellow-300' });
    return badges;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Card className="bg-gray-800/50 border-gray-700/50 hover:border-gray-600/50 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl hover:shadow-blue-500/10 overflow-hidden">
        {/* Banner Image (wide, ratio ~16:9) */}
        <div className="relative h-48 bg-gradient-to-br from-gray-700 to-gray-800 overflow-hidden">
          {hub.bannerUrl && !imageError ? (
            <Image
              src={hub.bannerUrl}
              alt={`${hub.name} banner`}
              fill
              className="object-cover transition-transform duration-300 group-hover:scale-105"
              onError={() => setImageError(true)}
            />
          ) : (
            <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center">
              <div className="text-6xl opacity-50">🌐</div>
            </div>
          )}

          {/* Overlay with badges */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

          {/* Status badges */}
          <div className="absolute top-3 left-3 flex flex-wrap gap-1">
            {getBadges().slice(0, 2).map((badge, i) => (
              <Badge key={i} className={`text-xs px-2 py-1 ${badge.color} border-none`}>
                {badge.label}
              </Badge>
            ))}
          </div>

          {/* NSFW indicator */}
          {hub.nsfw && (
            <div className="absolute top-3 right-3">
              <Badge className="bg-red-500/20 text-red-300 border-red-500/30">
                18+
              </Badge>
            </div>
          )}

          {/* Hub icon overlay - Increased size for better visibility */}
          <div className="absolute bottom-3 left-3">
            <div className="w-16 h-16 rounded-xl bg-gray-900/80 backdrop-blur-sm border border-gray-600/50 flex items-center justify-center overflow-hidden">
              <Image
                src={hub.iconUrl}
                alt={`${hub.name} icon`}
                width={56}
                height={56}
                className="rounded-lg"
              />
            </div>
          </div>
        </div>

        <CardContent className="p-6">
          {/* Hub Name (bold, large) */}
          <div className="flex items-start justify-between mb-3">
            <h3 className="text-xl font-bold text-white group-hover:text-blue-300 transition-colors line-clamp-1">
              {hub.name}
            </h3>
            {rating && (
              <div className="flex items-center gap-1 text-yellow-400">
                <Star className="w-4 h-4 fill-current" />
                <span className="text-sm font-medium">{rating.toFixed(1)}</span>
              </div>
            )}
          </div>

          {/* Short description (2-line max, ellipsis) */}
          <p className="text-gray-400 text-sm mb-4 line-clamp-2 leading-relaxed">
            {hub.shortDescription || 'Join this amazing community and connect with like-minded people across Discord servers.'}
          </p>

          {/* 🔸 Tags/Topics */}
          <div className="flex flex-wrap gap-2 mb-4">
            {hub.tags.slice(0, 3).map((tag, i) => (
              <Badge key={i} variant="secondary" className="text-xs bg-gray-700/50 text-gray-300 hover:bg-gray-600/50">
                #{tag.name}
              </Badge>
            ))}
            {hub.tags.length > 3 && (
              <Badge variant="secondary" className="text-xs bg-gray-700/50 text-gray-300">
                +{hub.tags.length - 3}
              </Badge>
            )}
          </div>

          {/* Stats Row */}
          <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
            {/* Server Count */}
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-blue-400" />
              <span className="text-gray-300">{serverCount.toLocaleString()} Servers</span>
            </div>

            {/* Message Activity */}
            <div className="flex items-center gap-2">
              <Activity className="w-4 h-4 text-green-400" />
              <span className="text-gray-300">~{(dailyActivity / 1000).toFixed(1)}K/day</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              asChild
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none text-white font-medium w-full sm:w-auto"
            >
              <Link href={`/hubs/${hub.id}`}>
                Join Hub
              </Link>
            </Button>

            {/* View Details button (fades in on hover, always visible on mobile) */}
            <Button
              variant="outline"
              size="sm"
              className="sm:opacity-0 sm:group-hover:opacity-100 transition-opacity border-gray-600 hover:bg-gray-700/50 w-full sm:w-auto"
              onMouseEnter={() => setShowDetails(true)}
              onMouseLeave={() => setShowDetails(false)}
              onClick={() => setShowDetails(!showDetails)}
            >
              <Eye className="w-4 h-4 sm:mr-0 mr-2" />
              <span className="sm:hidden">View Details</span>
            </Button>
          </div>

          {/* Quick stats on hover */}
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-700/50"
            >
              <div className="grid grid-cols-3 gap-2 text-xs text-gray-400">
                <div className="text-center">
                  <Heart className="w-3 h-3 mx-auto mb-1 text-red-400" />
                  <div>{upvoteCount} upvotes</div>
                </div>
                <div className="text-center">
                  <TrendingUp className="w-3 h-3 mx-auto mb-1 text-green-400" />
                  <div className={getActivityColor(hub.activityLevel)}>
                    {hub.activityLevel.toLowerCase()}
                  </div>
                </div>
                <div className="text-center">
                  <Flame className="w-3 h-3 mx-auto mb-1 text-orange-400" />
                  <div>{hub.messageCount.toLocaleString()} total</div>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
}
