"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import { Connection, Hub, ServerData } from "@/lib/generated/prisma/client";
import {
  Copy,
  ExternalLink,
  Hash,
  Loader2,
  Palette,
  Plus,
  Save,
  X,
  Settings,
  Zap,
  Eye,
  Link,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { useState } from "react";
import { ChannelSelector } from "./channel-selector";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useRouter } from "next/navigation";

interface ConnectionEditFormClientProps {
  connection: Connection & { hub: Hub; server: ServerData };
}

export function ConnectionEditFormClient({ connection }: ConnectionEditFormClientProps) {
  const [isConnected, setIsConnected] = useState(connection?.connected || false);
  const [isCompact, setIsCompact] = useState(connection?.compact || false);
  const [embedColor, setEmbedColor] = useState(connection?.embedColor || "#5865F2");
  const [inviteUrl, setInviteUrl] = useState(connection?.invite || "");
  const [channelId, setChannelId] = useState(connection?.channelId || "");
  const [isGeneratingInvite, setIsGeneratingInvite] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const { toast } = useToast();
  const router = useRouter();

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text).then(
      () => {
        toast({
          description: `${label} copied to clipboard`,
        });
      },
      (err) => {
        console.error("Could not copy text: ", err);
        toast({
          variant: "destructive",
          description: "Failed to copy to clipboard",
        });
      }
    );
  };

  const generateInvite = async () => {
    try {
      setIsGeneratingInvite(true);
      const response = await fetch(
        `/api/dashboard/connections/${connection.id}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            action: "generateInvite",
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to generate invite");
      }

      const data = await response.json();
      setInviteUrl(data.invite);

      toast({
        title: "Invite Generated",
        description: "A new server invite has been generated.",
      });
    } catch (error) {
      console.error("Error generating invite:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to generate invite",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingInvite(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsSaving(true);
      const response = await fetch(
        `/api/dashboard/connections/${connection.id}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            connected: isConnected,
            compact: isCompact,
            embedColor: embedColor || null,
            invite: inviteUrl || null,
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update connection");
      }

      toast({
        title: "Connection Updated",
        description: "The connection has been updated successfully.",
      });

      // Redirect back to connection overview
      router.push(`/dashboard/connections/${connection.id}`);
    } catch (error) {
      console.error("Error updating connection:", error);
      toast({
        title: "Update Failed",
        description: error instanceof Error ? error.message : "Failed to update connection",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
      <CardHeader className="px-4 sm:px-6">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-500/10 border border-blue-500/20">
            <Settings className="h-5 w-5 text-blue-400" />
          </div>
          <div>
            <CardTitle>Connection Settings</CardTitle>
            <CardDescription>
              Configure how this connection works
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6 px-4 sm:px-6">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="connected" className="text-base font-medium flex items-center gap-2">
              <Zap className="h-4 w-4" />
              Connection Status
            </Label>
            <p className="text-sm text-gray-400">
              Enable or disable message sharing for this connection
            </p>
          </div>
          <Switch
            id="connected"
            checked={isConnected}
            onCheckedChange={setIsConnected}
          />
        </div>

        <Separator className="bg-gray-800/50" />

        {/* Compact Mode */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="compact" className="text-base font-medium flex items-center gap-2">
              <Eye className="h-4 w-4" />
              Compact Mode
            </Label>
            <p className="text-sm text-gray-400">
              Show messages in a more compact format
            </p>
          </div>
          <Switch
            id="compact"
            checked={isCompact}
            onCheckedChange={setIsCompact}
          />
        </div>

        <Separator className="bg-gray-800/50" />

        {/* Embed Color */}
        <div className="space-y-3">
          <Label htmlFor="embedColor" className="text-base font-medium flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Embed Color
          </Label>
          <p className="text-sm text-gray-400 mb-3">
            Choose the color for message embeds from this connection
          </p>
          <div className="flex items-center gap-3">
            <div className="relative">
              <Input
                id="embedColor"
                type="color"
                value={embedColor}
                onChange={(e) => setEmbedColor(e.target.value)}
                className="w-16 h-10 p-1 border border-gray-700 bg-gray-800 rounded cursor-pointer"
              />
            </div>
            <Input
              type="text"
              value={embedColor}
              onChange={(e) => setEmbedColor(e.target.value)}
              placeholder="#5865F2"
              className="flex-1 bg-gray-800 border-gray-700 text-white"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => setEmbedColor("#5865F2")}
              className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              Reset
            </Button>
          </div>
        </div>

        <Separator className="bg-gray-800/50" />

        {/* Server Invite */}
        <div className="space-y-3">
          <Label htmlFor="invite" className="text-base font-medium flex items-center gap-2">
            <Link className="h-4 w-4" />
            Server Invite Link
          </Label>
          <p className="text-sm text-gray-400 mb-3">
            Optional invite link to your Discord server
          </p>
          <div className="flex items-center gap-2">
            <Input
              id="invite"
              type="url"
              value={inviteUrl}
              onChange={(e) => setInviteUrl(e.target.value)}
              placeholder="https://discord.gg/..."
              className="flex-1 bg-gray-800 border-gray-700 text-white"
            />
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyToClipboard(inviteUrl, "Invite URL")}
              disabled={!inviteUrl}
              className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              <Copy className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={generateInvite}
              disabled={isGeneratingInvite}
              className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
            >
              {isGeneratingInvite ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
          {inviteUrl && (
            <div className="flex items-center gap-2 text-sm">
              <CheckCircle className="h-4 w-4 text-green-400" />
              <span className="text-green-400">Invite link configured</span>
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="h-6 px-2 text-xs text-blue-400 hover:text-blue-300"
              >
                <a href={inviteUrl} target="_blank" rel="noopener noreferrer">
                  <ExternalLink className="h-3 w-3 mr-1" />
                  Test
                </a>
              </Button>
            </div>
          )}
        </div>

        <Separator className="bg-gray-800/50" />

        {/* Channel Information */}
        <div className="space-y-3">
          <Label className="text-base font-medium flex items-center gap-2">
            <Hash className="h-4 w-4" />
            Connected Channel
          </Label>
          <div className="p-3 rounded-lg border border-gray-800/50 bg-gray-900/20">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Hash className="h-4 w-4 text-gray-400" />
                <span className="font-mono text-sm text-white">{channelId}</span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(channelId, "Channel ID")}
                className="h-6 px-2 text-xs text-gray-400 hover:text-white"
              >
                <Copy className="h-3 w-3" />
              </Button>
            </div>
          </div>
          <p className="text-xs text-gray-500">
            Channel changes must be made through Discord bot commands
          </p>
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-none text-white font-medium px-6"
          >
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
