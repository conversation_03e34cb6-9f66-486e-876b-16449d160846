"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, RefreshCw } from "lucide-react";

export function FixAccountButton() {
  const [isFixing, setIsFixing] = useState(false);
  const { toast } = useToast();
  const router = useRouter();

  const handleFixAccount = async () => {
    setIsFixing(true);

    try {
      const response = await fetch("/api/auth/fix-accounts");

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fix account");
      }

      toast({
        title: "Account Fixed",
        description: "Your account has been fixed successfully.",
      });

      // Refresh the page to apply the changes
      router.refresh();
    } catch (error) {
      console.error("Error fixing account:", error);

      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to fix account",
        variant: "destructive",
      });
    } finally {
      setIsFixing(false);
    }
  };

  return (
    <Button
      onClick={handleFixAccount}
      disabled={isFixing}
      variant="outline"
      size="sm"
    >
      {isFixing ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          Fixing...
        </>
      ) : (
        <>
          <RefreshCw className="h-4 w-4 mr-2" />
          Fix Account
        </>
      )}
    </Button>
  );
}
