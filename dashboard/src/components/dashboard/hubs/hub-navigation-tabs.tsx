'use client';

import { UnderlinedTabs } from '@/components/dashboard/underlined-tabs';
import {
  AlertTriangle,
  Edit,
  FileText,
  Home,
  MessageSquare,
  Settings,
  Shield,
  Users,
} from 'lucide-react';

interface HubNavigationTabsProps {
  hubId: string;
  currentTab: string;
  canModerate?: boolean;
  canEdit?: boolean;
}

export function HubNavigationTabs({
  hubId,
  currentTab,
  canModerate = false,
  canEdit = false,
}: HubNavigationTabsProps) {
  return (
    <div className="px-6">
      <UnderlinedTabs
        defaultValue={currentTab}
        className="w-full"
        navigational={true}
        tabs={[
          {
            value: 'overview',
            label: 'Overview',
            color: 'indigo' as const,
            icon: <MessageSquare className="h-4 w-4" />,
            href: `/dashboard/hubs/${hubId}`,
          },
          // Only show edit tab if user can edit
          ...(canEdit
            ? [
                {
                  value: 'edit',
                  label: 'Edit Hub',
                  color: 'blue' as const,
                  icon: <Edit className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/edit`,
                },
              ]
            : []),
          // Only show members tab if user can moderate
          ...(canModerate
            ? [
                {
                  value: 'members',
                  label: 'Members',
                  color: 'blue' as const,
                  icon: <Users className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/members`,
                },
              ]
            : []),
          // Only show connections tab if user can moderate
          ...(canModerate
            ? [
                {
                  value: 'connections',
                  label: 'Connections',
                  color: 'green' as const,
                  icon: <Home className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/connections`,
                },
              ]
            : []),
          // Add moderation tab if user has permission
          ...(canModerate
            ? [
                {
                  value: 'moderation',
                  label: 'Moderation',
                  color: 'purple' as const,
                  icon: <Shield className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/moderation`,
                },
                {
                  value: 'anti-swear',
                  label: 'Anti-Swear',
                  color: 'red' as const,
                  icon: <AlertTriangle className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/anti-swear`,
                },
              ]
            : []),
          // Add logging tab if user can edit
          ...(canEdit
            ? [
                {
                  value: 'logging',
                  label: 'Logging',
                  color: 'purple' as const,
                  icon: <FileText className="h-4 w-4" />,
                  href: `/dashboard/hubs/${hubId}/logging`,
                },
              ]
            : []),
          {
            value: 'settings',
            label: 'Settings',
            color: 'orange' as const,
            icon: <Settings className="h-4 w-4" />,
            href: `/dashboard/hubs/${hubId}/settings`,
          },
        ]}
      />
    </div>
  );
}
