"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown, PlusCircle, Settings } from "lucide-react";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import { PermissionLevel } from "@/lib/constants";
import { useToast } from "@/components/ui/use-toast";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

interface Hub {
  id: string;
  name: string;
  iconUrl: string;
  permissionLevel: PermissionLevel;
}

export function QuickHubSwitcher() {
  const [open, setOpen] = useState(false);
  const [commandOpen, setCommandOpen] = useState(false);
  const [hubs, setHubs] = useState<Hub[]>([]);
  const [selectedHub, setSelectedHub] = useState<Hub | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  // Load hubs on component mount
  useEffect(() => {
    const fetchHubs = async () => {
      try {
        const response = await fetch("/api/hubs?moderated=true");
        if (!response.ok) {
          throw new Error("Failed to fetch hubs");
        }
        const data = await response.json();
        setHubs(data.hubs);

        // Try to get current hub from URL
        const pathParts = window.location.pathname.split("/");
        const hubIdIndex = pathParts.indexOf("hubs") + 1;

        if (hubIdIndex > 0 && hubIdIndex < pathParts.length) {
          const currentHubId = pathParts[hubIdIndex];
          const currentHub = data.hubs.find(
            (hub: Hub) => hub.id === currentHubId,
          );
          if (currentHub) {
            setSelectedHub(currentHub);
          }
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching hubs:", error);
        toast({
          title: "Error",
          description: "Failed to load your hubs. Please refresh the page.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };

    fetchHubs();
  }, [toast]);

  // Handle keyboard shortcut to open command dialog
  useEffect(() => {
    const down = (e: KeyboardEvent) => {
      if (e.key === "h" && (e.metaKey || e.ctrlKey)) {
        e.preventDefault();
        setCommandOpen((open) => !open);
      }
    };

    document.addEventListener("keydown", down);
    return () => document.removeEventListener("keydown", down);
  }, []);

  const handleHubSelect = (hub: Hub) => {
    setSelectedHub(hub);
    setOpen(false);
    setCommandOpen(false);
    router.push(`/dashboard/hubs/${hub.id}`);
  };

  const getPermissionBadge = (level: PermissionLevel) => {
    switch (level) {
      case PermissionLevel.OWNER:
        return (
          <Badge className="bg-indigo-500/20 text-indigo-300 border-indigo-500/30">
            Owner
          </Badge>
        );
      case PermissionLevel.MANAGER:
        return (
          <Badge className="bg-blue-500/20 text-blue-300 border-blue-500/30">
            Manager
          </Badge>
        );
      case PermissionLevel.MODERATOR:
        return (
          <Badge className="bg-purple-500/20 text-purple-300 border-purple-500/30">
            Moderator
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            aria-label="Select a hub"
            className="w-[240px] justify-between bg-gray-800/50 border-gray-700/50 hover:bg-gray-700/50 hover:text-white"
          >
            {isLoading ? (
              <span className="text-gray-400">Loading hubs...</span>
            ) : selectedHub ? (
              <div className="flex items-center gap-2 truncate">
                <div className="relative h-5 w-5 rounded-full overflow-hidden">
                  <Image
                    src={selectedHub.iconUrl}
                    alt={selectedHub.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <span className="truncate">{selectedHub.name}</span>
              </div>
            ) : (
              <span className="text-gray-400">Select a hub</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[240px] p-0 bg-gray-900 border border-gray-800">
          <Command>
            <CommandInput
              placeholder="Search hubs..."
              className="h-9 border-b border-gray-800 bg-transparent"
            />
            <CommandList>
              <CommandEmpty>No hubs found.</CommandEmpty>
              <CommandGroup heading="Your Hubs">
                {hubs.map((hub) => (
                  <CommandItem
                    key={hub.id}
                    value={hub.name}
                    onSelect={() => handleHubSelect(hub)}
                    className="flex items-center gap-2 cursor-pointer"
                  >
                    <div className="relative h-5 w-5 rounded-full overflow-hidden">
                      <Image
                        src={hub.iconUrl}
                        alt={hub.name}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <span className="truncate flex-1">{hub.name}</span>
                    {getPermissionBadge(hub.permissionLevel)}
                    <Check
                      className={cn(
                        "ml-auto h-4 w-4",
                        selectedHub?.id === hub.id
                          ? "opacity-100"
                          : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
              <CommandSeparator className="bg-gray-800" />
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    router.push("/dashboard/hubs/create");
                  }}
                  className="cursor-pointer"
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  <span>Create New Hub</span>
                </CommandItem>
                {selectedHub && (
                  <CommandItem
                    onSelect={() => {
                      setOpen(false);
                      router.push(`/dashboard/hubs/${selectedHub.id}/edit`);
                    }}
                    className="cursor-pointer"
                  >
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Hub Settings</span>
                  </CommandItem>
                )}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <CommandDialog open={commandOpen} onOpenChange={setCommandOpen}>
        <CommandInput placeholder="Search hubs..." />
        <CommandList>
          <CommandEmpty>No hubs found.</CommandEmpty>
          <CommandGroup heading="Your Hubs">
            {hubs.map((hub) => (
              <CommandItem
                key={hub.id}
                onSelect={() => handleHubSelect(hub)}
                className="flex items-center gap-2 cursor-pointer"
              >
                <div className="relative h-5 w-5 rounded-full overflow-hidden">
                  <Image
                    src={hub.iconUrl}
                    alt={hub.name}
                    fill
                    className="object-cover"
                  />
                </div>
                <span className="truncate flex-1">{hub.name}</span>
                {getPermissionBadge(hub.permissionLevel)}
              </CommandItem>
            ))}
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="Actions">
            <CommandItem
              onSelect={() => {
                setCommandOpen(false);
                router.push("/dashboard/hubs/create");
              }}
              className="cursor-pointer"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              <span>Create New Hub</span>
            </CommandItem>
            <CommandItem
              onSelect={() => {
                setCommandOpen(false);
                router.push("/dashboard/hubs");
              }}
              className="cursor-pointer"
            >
              <Settings className="mr-2 h-4 w-4" />
              <span>Manage All Hubs</span>
            </CommandItem>
          </CommandGroup>
          <CommandSeparator />
          <CommandGroup heading="Tip">
            <div className="px-2 py-1 text-xs text-gray-400">
              Press <kbd className="bg-gray-800 px-1 rounded">Ctrl</kbd> +{" "}
              <kbd className="bg-gray-800 px-1 rounded">H</kbd> anywhere to open
              this menu
            </div>
          </CommandGroup>
        </CommandList>
      </CommandDialog>
    </>
  );
}
