"use client";

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { useUploadThing } from '@/lib/uploadthing-utils';
import { cn } from '@/lib/utils';
import {
  AlertCircle,
  Image as ImageIcon,
  Loader2,
  RefreshCw,
  Trash2,
  Upload,
  X
} from 'lucide-react';
import Image from 'next/image';
import React, { useCallback, useRef, useState } from 'react';

interface HubBannerUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  hubId: string;
  currentBannerUrl?: string;
  hubName?: string;
  onBannerUpdate?: (bannerUrl: string | null) => void;
}

export function HubBannerUploadModal({
  isOpen,
  onClose,
  hubId,
  currentBannerUrl,
  hubName,
  onBannerUpdate,
}: HubBannerUploadModalProps) {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentBannerUrl || null);
  const [isUploading, setIsUploading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // UploadThing hook
  const { startUpload } = useUploadThing("hubBannerUploader", {
    onClientUploadComplete: (res) => {
      const uploadedFile = res?.[0];
      if (uploadedFile) {
        // Clean up old preview URL
        if (previewUrl && previewUrl.startsWith('blob:')) {
          URL.revokeObjectURL(previewUrl);
        }

        setPreviewUrl(uploadedFile.ufsUrl);
        setSelectedFile(null);
        setHasChanges(false);

        if (onBannerUpdate) {
          onBannerUpdate(uploadedFile.ufsUrl);
        }

        toast({
          title: "Banner Updated",
          description: "Your hub banner has been successfully updated.",
        });

        onClose();
      }
      setIsUploading(false);
    },
    onUploadError: (error) => {
      console.error('Upload error:', error);
      toast({
        title: "Upload Failed",
        description: error.message || "Failed to upload banner",
        variant: "destructive",
      });
      setIsUploading(false);
    },
  });

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid File Type",
        description: "Please select an image file (PNG, JPG, GIF, etc.)",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (4MB limit)
    if (file.size > 4 * 1024 * 1024) {
      toast({
        title: "File Too Large",
        description: "Please select an image smaller than 4MB",
        variant: "destructive",
      });
      return;
    }

    setSelectedFile(file);
    setHasChanges(true);

    // Create preview URL
    const url = URL.createObjectURL(file);
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(url);
  }, [previewUrl, toast]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // Handle file input change
  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  // Upload banner using UploadThing
  const handleUploadBanner = async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    try {
      await startUpload([selectedFile], { hubId });
    } catch (error) {
      console.error('Error starting upload:', error);
      toast({
        title: "Upload Failed",
        description: error instanceof Error ? error.message : "Failed to start upload",
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  // Reset to original state
  const handleReset = () => {
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(currentBannerUrl || null);
    setSelectedFile(null);
    setHasChanges(false);
  };

  // Remove banner
  const handleRemoveBanner = async () => {
    // This would need an API endpoint to remove the banner
    // For now, we'll just clear the preview
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setSelectedFile(null);
    setHasChanges(true);
  };

  // Handle modal close
  const handleClose = () => {
    if (previewUrl && previewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(previewUrl);
    }
    setSelectedFile(null);
    setHasChanges(false);
    setPreviewUrl(currentBannerUrl || null);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-2xl bg-gray-900 border-gray-800">
        <DialogHeader>
          <DialogTitle className="text-white">Update Hub Banner</DialogTitle>
          <DialogDescription className="text-gray-400">
            Upload a new banner for {hubName || 'your hub'}. Recommended size: 1920x1080px or similar aspect ratio.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current/Preview Banner */}
          <div className="relative">
            <div className="aspect-[16/9] rounded-lg border-2 border-gray-700/50 overflow-hidden bg-gray-800 shadow-lg">
              {previewUrl ? (
                <Image
                  src={previewUrl}
                  alt={hubName || 'Hub banner'}
                  width={640}
                  height={360}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <ImageIcon className="h-12 w-12 text-gray-500 mx-auto mb-2" />
                    <p className="text-gray-500 text-sm">No banner set</p>
                  </div>
                </div>
              )}
            </div>
            {previewUrl && (
              <Button
                variant="destructive"
                size="sm"
                className="absolute top-2 right-2 h-8 w-8 rounded-full p-0"
                onClick={handleRemoveBanner}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* Upload Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
              isDragging
                ? "border-indigo-500 bg-indigo-500/10"
                : "border-gray-600 hover:border-gray-500"
            )}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="hidden"
            />

            <div className="space-y-3">
              <div className="w-12 h-12 mx-auto rounded-full bg-gray-700/50 flex items-center justify-center">
                <Upload className="w-6 h-6 text-gray-400" />
              </div>

              <div>
                <p className="text-sm font-medium text-white mb-1">
                  {isDragging ? "Drop your image here" : "Upload Banner"}
                </p>
                <p className="text-xs text-gray-400 mb-3">
                  Drag and drop or click to browse
                </p>

                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white h-8 px-3"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Choose File
                </Button>
              </div>
            </div>
          </div>

          {/* File Info */}
          {selectedFile && (
            <div className="bg-gray-800/50 rounded-lg p-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded bg-indigo-500/20 flex items-center justify-center">
                  <Upload className="w-4 h-4 text-indigo-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">
                    {selectedFile.name}
                  </p>
                  <p className="text-xs text-gray-400">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isUploading}
            className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
          >
            Cancel
          </Button>
          
          {hasChanges && (
            <Button
              variant="ghost"
              onClick={handleReset}
              disabled={isUploading}
              className="text-gray-400 hover:text-white hover:bg-gray-800"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Reset
            </Button>
          )}

          <Button
            onClick={handleUploadBanner}
            disabled={!selectedFile || isUploading}
            className="bg-indigo-600 hover:bg-indigo-700 text-white"
          >
            {isUploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Upload Banner
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
