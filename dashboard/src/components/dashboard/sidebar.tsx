"use client";

import { cn } from "@/lib/utils";
import { motion } from "motion/react";
import {
  Activity,
  Bell,
  ChevronLeft,
  ChevronRight,
  HelpCircle,
  Home,
  LayoutDashboard,
  MessageSquare,
  Scale,
  Settings,
  Shield,
  Users,
  Zap,
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useDashboardLayout } from "./layout-provider";

interface SidebarNavItemProps {
  href: string;
  icon: React.ElementType;
  label: string;
  active?: boolean;
  badge?: number | string;
  isCollapsed?: boolean;
}

function SidebarNavItem({
  href,
  icon: Icon,
  label,
  active,
  badge,
  isCollapsed = false,
}: SidebarNavItemProps) {
  const content = (
    <Link
      href={href}
      className={cn(
        "flex items-center rounded-lg px-3 py-2.5 text-sm transition-all relative",
        isCollapsed ? "justify-center" : "justify-between",
        active
          ? "bg-gradient-to-r from-indigo-500/20 to-purple-500/10 text-white font-medium"
          : "text-gray-400 hover:text-white hover:bg-gray-800/50"
      )}
    >
      <div className={cn("flex items-center", isCollapsed ? "justify-center" : "gap-3")}>
        <div
          className={cn(
            "flex items-center justify-center w-6 h-6 rounded-md",
            active ? "bg-indigo-500/20 text-indigo-400" : "text-gray-400"
          )}
        >
          <Icon className="h-4 w-4" />
        </div>
        {!isCollapsed && <span>{label}</span>}
      </div>
      {!isCollapsed && badge && (
        <div className="bg-indigo-500/20 text-indigo-300 text-xs font-medium px-2 py-0.5 rounded-full">
          {badge}
        </div>
      )}
      {active && (
        <motion.div
          layoutId="activeIndicator"
          className="absolute right-0 w-1 h-8 bg-indigo-500 rounded-l-full"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </Link>
  );

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {content}
          </TooltipTrigger>
          <TooltipContent side="right" className="ml-2">
            <p>{label}</p>
            {badge && <span className="ml-2 text-xs opacity-75">({badge})</span>}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return content;
}

interface SidebarSectionProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
  isCollapsed?: boolean;
}

function SidebarSection({
  title,
  children,
  defaultOpen = true,
  isCollapsed = false,
}: SidebarSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  if (isCollapsed) {
    return (
      <div className="py-2">
        <div className="border-t border-gray-800/30 my-2"></div>
        <div className="space-y-1">
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className="py-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-1.5 text-xs font-semibold text-gray-400 uppercase tracking-wider hover:text-gray-300 transition-colors"
      >
        <span>{title}</span>
        <motion.div
          animate={{ rotate: isOpen ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRight className="h-3.5 w-3.5" />
        </motion.div>
      </button>
      <motion.div
        className="mt-1 space-y-1 overflow-hidden"
        initial={{ height: defaultOpen ? "auto" : 0 }}
        animate={{ height: isOpen ? "auto" : 0 }}
        transition={{ duration: 0.2 }}
      >
        {children}
      </motion.div>
    </div>
  );
}

interface DashboardSidebarProps {
  hasModeratorAccess?: boolean;
}

export function DashboardSidebar({ hasModeratorAccess = false }: DashboardSidebarProps) {
  const pathname = usePathname();
  const { sidebarCollapsed: isCollapsed, setSidebarCollapsed, isHydrated } = useDashboardLayout();

  // Toggle collapsed state
  const toggleCollapsed = () => {
    setSidebarCollapsed(!isCollapsed);
  };

  // Use default width until hydrated to prevent hydration mismatch
  const currentWidth = isHydrated ? (isCollapsed ? 80 : 256) : 256;

  return (
    <motion.div
      className="hidden md:flex md:flex-col md:fixed md:inset-y-0"
      style={{ zIndex: 40 }}
      initial={false}
      animate={{ width: currentWidth }}
      transition={{ duration: isHydrated ? 0.3 : 0, ease: "easeInOut" }}
    >
      <div className="flex flex-col flex-grow border-r border-gray-800/50 bg-gradient-to-b from-gray-900 to-gray-950 overflow-y-auto" data-tour="sidebar">
        <div className="flex items-center h-16 flex-shrink-0 border-b border-gray-800/50">
          {(isCollapsed && isHydrated) ? (
            /* Collapsed layout - centered logo with toggle on right */
            <>
              <div className="flex items-center justify-center flex-1 px-2">
                <Link href="/dashboard" className="group">
                  <motion.div
                    whileHover={{ rotate: 5 }}
                    transition={{ duration: 0.2 }}
                    className="flex-shrink-0"
                  >
                    <Image
                      alt="InterChat"
                      src="/interchat.png"
                      height={32}
                      width={32}
                      className="rounded-full border border-gray-700/50 group-hover:border-indigo-500/50 transition-colors"
                    />
                  </motion.div>
                </Link>
              </div>
              <div className="flex items-center px-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleCollapsed}
                  className="h-8 w-8 text-gray-400 hover:text-white hover:bg-gray-800/50 flex-shrink-0 rounded-lg border border-transparent hover:border-gray-700/50 transition-all duration-200"
                  title="Expand sidebar"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </>
          ) : (
            /* Expanded layout - logo on left, toggle on right */
            <>
              {/* Logo section with proper padding */}
              <div className="flex items-center flex-1 px-4 min-w-0">
                <Link href="/dashboard" className="flex items-center gap-3 group min-w-0 max-w-full">
                  <motion.div
                    whileHover={{ rotate: 5 }}
                    transition={{ duration: 0.2 }}
                    className="flex-shrink-0"
                  >
                    <Image
                      alt="InterChat"
                      src="/interchat.png"
                      height={32}
                      width={32}
                      className="rounded-full border border-gray-700/50 group-hover:border-indigo-500/50 transition-colors"
                    />
                  </motion.div>
                  <motion.span
                    className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400 truncate"
                    whileHover={{ scale: 1.03 }}
                    transition={{ duration: 0.2 }}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                  >
                    InterChat
                  </motion.span>
                </Link>
              </div>

              {/* Toggle button section with proper spacing */}
              <div className="flex items-center px-3">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={toggleCollapsed}
                  className="h-9 w-9 text-gray-400 hover:text-white hover:bg-gray-800/50 flex-shrink-0 rounded-lg border border-transparent hover:border-gray-700/50 transition-all duration-200 shadow-sm"
                  title="Collapse sidebar"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
              </div>
            </>
          )}
        </div>

        <div className="flex-grow flex flex-col px-3 py-4 space-y-1">
          <SidebarNavItem
            href="/dashboard"
            icon={LayoutDashboard}
            label="Dashboard"
            active={pathname === "/dashboard"}
            isCollapsed={isCollapsed && isHydrated}
          />

          {(!isCollapsed || !isHydrated) && <div className="border-t border-gray-800/30 my-2"></div>}

          <SidebarSection title="Hub Management" isCollapsed={isCollapsed && isHydrated}>
            <SidebarNavItem
              href="/dashboard/hubs"
              icon={MessageSquare}
              label="My Hubs"
              active={pathname.startsWith("/dashboard/hubs")}
              isCollapsed={isCollapsed && isHydrated}
            />
            <SidebarNavItem
              href="/dashboard/servers"
              icon={Home}
              label="My Servers"
              active={pathname.startsWith("/dashboard/servers")}
              isCollapsed={isCollapsed && isHydrated}
            />
            <SidebarNavItem
              href="/dashboard/my-appeals"
              icon={Scale}
              label="My Appeals"
              active={pathname.startsWith("/dashboard/my-appeals")}
              isCollapsed={isCollapsed && isHydrated}
            />
          </SidebarSection>

          <SidebarSection title="Moderation" isCollapsed={isCollapsed && isHydrated}>
            <SidebarNavItem
              href="/dashboard/moderation/reports"
              icon={Shield}
              label="Reports"
              active={pathname.startsWith("/dashboard/moderation/reports")}
              badge="New"
              isCollapsed={isCollapsed && isHydrated}
            />
            <SidebarNavItem
              href="/dashboard/moderation/blacklist"
              icon={Users}
              label="Blacklists"
              active={pathname.startsWith("/dashboard/moderation/blacklist")}
              isCollapsed={isCollapsed && isHydrated}
            />
            {hasModeratorAccess && (
              <SidebarNavItem
                href="/dashboard/appeals"
                icon={Bell}
                label="Appeals"
                active={pathname.startsWith("/dashboard/appeals")}
                badge="New"
                isCollapsed={isCollapsed && isHydrated}
              />
            )}
          </SidebarSection>

          <SidebarSection title="Analytics" defaultOpen={false} isCollapsed={isCollapsed && isHydrated}>
            <SidebarNavItem
              href="/dashboard/analytics/overview"
              icon={Activity}
              label="Overview"
              active={pathname.startsWith("/dashboard/analytics/overview")}
              isCollapsed={isCollapsed && isHydrated}
            />
            <SidebarNavItem
              href="/dashboard/analytics/connections"
              icon={Zap}
              label="Connections"
              active={pathname.startsWith("/dashboard/analytics/connections")}
              isCollapsed={isCollapsed && isHydrated}
            />
          </SidebarSection>

          {(!isCollapsed || !isHydrated) && <div className="border-t border-gray-800/30 my-2"></div>}

          <SidebarNavItem
            href="/dashboard/settings"
            icon={Settings}
            label="Settings"
            active={pathname.startsWith("/dashboard/settings")}
            isCollapsed={isCollapsed && isHydrated}
          />

          <SidebarNavItem
            href="/support"
            icon={HelpCircle}
            label="Help & Support"
            active={pathname.startsWith("/support")}
            isCollapsed={isCollapsed && isHydrated}
          />
        </div>

        {(!isCollapsed || !isHydrated) && (
          <div className="p-3 m-3 bg-gradient-to-r from-indigo-900/20 to-purple-900/20 rounded-lg border border-indigo-500/20">
            <div className="flex items-center gap-3 mb-2">
              <div className="bg-indigo-500/20 p-2 rounded-md">
                <Zap className="h-5 w-5 text-indigo-400" />
              </div>
              <div>
                <h4 className="font-medium text-white text-sm">Pro Features</h4>
                <p className="text-xs text-gray-400">Unlock advanced features</p>
              </div>
            </div>
            <Link href="/pricing">
              <motion.button
                className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 text-white text-sm font-medium py-1.5 px-3 rounded-md transition-colors"
                whileHover={{ y: -2 }}
                whileTap={{ y: 0 }}
                transition={{ duration: 0.2 }}
              >
                Upgrade Now
              </motion.button>
            </Link>
          </div>
        )}
      </div>
    </motion.div>
  );
}
