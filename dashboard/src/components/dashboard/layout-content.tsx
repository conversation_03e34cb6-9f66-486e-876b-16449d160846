"use client";

import { AutoBreadcrumb } from "@/components/dashboard/breadcrumb";
import { DashboardTopBar } from "@/components/dashboard/topbar";
import { motion } from "motion/react";
import type { User } from "next-auth";
import type { ReactNode } from "react";
import { useState, useEffect } from "react";
import { useDashboardLayout } from "./layout-provider";

interface DashboardLayoutContentProps {
  user: User;
  children: ReactNode;
}

export function DashboardLayoutContent({ user, children }: DashboardLayoutContentProps) {
  const { sidebarWidth, isHydrated } = useDashboardLayout();
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const checkIsDesktop = () => {
      setIsDesktop(window.innerWidth >= 768);
    };

    checkIsDesktop();
    window.addEventListener('resize', checkIsDesktop);

    return () => window.removeEventListener('resize', checkIsDesktop);
  }, []);

  // Don't animate until hydrated to prevent hydration mismatch
  const shouldAnimate = isHydrated && isDesktop;

  return (
    <motion.div
      className="flex flex-col flex-1 overflow-hidden"
      initial={false}
      animate={{
        marginLeft: shouldAnimate ? sidebarWidth : 0
      }}
      transition={{ duration: shouldAnimate ? 0.3 : 0, ease: "easeInOut" }}
    >
      {/* Top bar */}
      <DashboardTopBar user={user} />

      {/* Main content with scrolling */}
      <main className="flex-1 overflow-y-auto dashboard-scrollbar bg-gradient-to-b from-gray-900 via-gray-900/95 to-gray-950 p-6 relative">
        {/* Background pattern - using pointer-events-none to allow clicks to pass through */}
        <div className="fixed inset-0 z-0 opacity-5 pointer-events-none">
          <div
            className="absolute inset-0 bg-grid-white bg-[size:30px_30px] [mask-image:radial-gradient(ellipse_80%_80%_at_50%_50%,#000_20%,transparent_120%)]"
            style={{ zIndex: -1 }}
          />
        </div>

        {/* Content */}
        <div className="relative z-10">
          <AutoBreadcrumb />
          {children}
        </div>
      </main>
    </motion.div>
  );
}
