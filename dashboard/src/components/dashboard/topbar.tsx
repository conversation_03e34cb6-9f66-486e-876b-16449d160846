"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Home, Menu, Search, X } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import type { User } from "next-auth";
import { useState } from "react";
import { UserNav } from "../user-nav";
import { QuickHubSwitcher } from "./hubs/quick-hub-switcher";
import { MobileSidebar } from "./mobile-sidebar";
import { NotificationDropdown } from "./notifications/notification-dropdown";
import { OnboardingHelpMenu } from "./onboarding/onboarding-help-menu";

export function DashboardTopBar({ user }: { user: User }) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  return (
    <div className="sticky top-0 z-40 flex h-16 flex-shrink-0 border-b border-gray-800/50 bg-gradient-to-r from-gray-900 to-gray-950 backdrop-blur-sm">
      <div className="flex flex-1 justify-between px-4">
        <div className="flex items-center md:hidden">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="text-gray-400 hover:text-white"
          >
            <Menu className="h-6 w-6" />
            <span className="sr-only">Open sidebar</span>
          </Button>
        </div>

        <div className="flex items-center gap-4">
          {/* Quick Hub Switcher */}
          <div className="hidden md:block">
            <QuickHubSwitcher />
          </div>

          {/* Search button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="text-gray-400 hover:text-white rounded-full h-9 w-9"
            data-tour="search-hubs"
          >
            <Search className="h-5 w-5" />
            <span className="sr-only">Search</span>
          </Button>

          {/* Animated search input */}
          <AnimatePresence>
            {isSearchOpen && (
              <motion.div
                initial={{ width: 0, opacity: 0 }}
                animate={{ width: 250, opacity: 1 }}
                exit={{ width: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative overflow-hidden z-20"
              >
                <Input
                  type="text"
                  placeholder="Search..."
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 h-9"
                  autoFocus
                />
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsSearchOpen(false)}
                  className="absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 rounded-full text-gray-400 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Notifications */}
          <div data-tour="notifications">
            <NotificationDropdown />
          </div>

          {/* Help */}
          <OnboardingHelpMenu />
        </div>

        {/* Mobile sidebar using portal to render outside the DOM hierarchy */}
        <MobileSidebar
          isOpen={isMobileMenuOpen}
          onClose={() => setIsMobileMenuOpen(false)}
          user={user}
        />

        {/* User dropdown */}
        <div className="flex items-center" data-tour="user-menu">
          <UserNav
            user={user}
            firstPage={{ name: "Home", icon: Home, href: "/" }}
          />
        </div>
      </div>
    </div>
  );
}
