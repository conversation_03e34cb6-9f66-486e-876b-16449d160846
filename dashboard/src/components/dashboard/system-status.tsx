"use client";

import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { motion } from "motion/react";
import { useInView } from "react-intersection-observer";
import { 
  Activity, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Clock,
  Zap,
  Database,
  Globe
} from "lucide-react";
import { cn } from "@/lib/utils";

interface StatusItem {
  name: string;
  status: "operational" | "degraded" | "outage" | "maintenance";
  icon: React.ElementType;
  description: string;
}

export function SystemStatus() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  // Mock system status data - in a real app, this would come from an API
  const systemStatus: StatusItem[] = [
    {
      name: "API Services",
      status: "operational",
      icon: Zap,
      description: "All API endpoints responding normally"
    },
    {
      name: "Database",
      status: "operational", 
      icon: Database,
      description: "Database queries executing within normal parameters"
    },
    {
      name: "Discord Bot",
      status: "operational",
      icon: Globe,
      description: "<PERSON><PERSON> is online and processing messages"
    },
    {
      name: "Web Dashboard",
      status: "operational",
      icon: Activity,
      description: "Dashboard fully functional"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "operational":
        return "text-green-400";
      case "degraded":
        return "text-yellow-400";
      case "outage":
        return "text-red-400";
      case "maintenance":
        return "text-blue-400";
      default:
        return "text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "operational":
        return CheckCircle;
      case "degraded":
        return AlertTriangle;
      case "outage":
        return XCircle;
      case "maintenance":
        return Clock;
      default:
        return CheckCircle;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "operational":
        return "Operational";
      case "degraded":
        return "Degraded Performance";
      case "outage":
        return "Service Outage";
      case "maintenance":
        return "Under Maintenance";
      default:
        return "Unknown";
    }
  };

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  const overallStatus = systemStatus.every(item => item.status === "operational") 
    ? "operational" 
    : systemStatus.some(item => item.status === "outage")
    ? "outage"
    : "degraded";

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={inView ? "show" : "hidden"}
      variants={container}
      className="h-full"
    >
      <Card className="border-gray-800 bg-gradient-to-b from-gray-900/50 to-gray-900/30 backdrop-blur-sm h-full">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <Activity className="h-5 w-5 text-indigo-400" />
            <div>
              <CardTitle className="text-xl font-bold">System Status</CardTitle>
              <CardDescription>Current status of InterChat services</CardDescription>
            </div>
          </div>
          
          {/* Overall Status */}
          <motion.div variants={item} className="mt-4">
            <div className="flex items-center gap-2 p-3 rounded-lg bg-gray-800/30 border border-gray-700/50">
              {(() => {
                const StatusIcon = getStatusIcon(overallStatus);
                return <StatusIcon className={cn("h-5 w-5", getStatusColor(overallStatus))} />;
              })()}
              <div>
                <p className="font-medium text-white">Overall Status</p>
                <p className={cn("text-sm", getStatusColor(overallStatus))}>
                  {getStatusText(overallStatus)}
                </p>
              </div>
            </div>
          </motion.div>
        </CardHeader>
        
        <CardContent>
          <div className="space-y-3">
            {systemStatus.map((service) => {
              const ServiceIcon = service.icon;
              const StatusIcon = getStatusIcon(service.status);
              
              return (
                <motion.div key={service.name} variants={item}>
                  <div className="flex items-center justify-between p-3 rounded-md border border-gray-800/50 hover:border-gray-700/50 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className="bg-gray-800/50 p-2 rounded-md">
                        <ServiceIcon className="h-4 w-4 text-gray-400" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-white">{service.name}</p>
                        <p className="text-xs text-gray-400">{service.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <StatusIcon className={cn("h-4 w-4", getStatusColor(service.status))} />
                      <span className={cn("text-xs font-medium", getStatusColor(service.status))}>
                        {getStatusText(service.status)}
                      </span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </div>
          
          <motion.div variants={item} className="mt-4 pt-3 border-t border-gray-800/30">
            <p className="text-xs text-gray-400 text-center">
              Last updated: {new Date().toLocaleTimeString()}
            </p>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
