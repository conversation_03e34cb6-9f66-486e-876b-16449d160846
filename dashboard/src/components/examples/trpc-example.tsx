"use client";

import { trpc } from "@/utils/trpc";
import { Button } from "@/components/ui/button";
import { useState } from "react";

export function TRPCExample() {
  const [hubName, setHubName] = useState("");
  const [hubDescription, setHubDescription] = useState("");

  // Use a simple tRPC query
  const helloQuery = trpc.user.getAccessibleHubs.useQuery(undefined, {
    retry: false,
  });

  // Use tRPC mutation to create a hub
  const createHubMutation = trpc.hub.createHub.useMutation({
    onSuccess: () => {
      // Invalidate the hubs query to refresh the data
      helloQuery.refetch();
      // Reset form
      setHubName("");
      setHubDescription("");
    },
  });

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createHubMutation.mutate({
      name: hubName,
      description: hubDescription,
      private: true,
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">tRPC Example</h2>

        <div className="mb-6">
          <h3 className="text-xl font-semibold mb-2">Create a Hub</h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="hubName" className="block mb-1">
                Hub Name
              </label>
              <input
                id="hubName"
                type="text"
                value={hubName}
                onChange={(e) => setHubName(e.target.value)}
                className="w-full p-2 border rounded"
                required
                minLength={3}
                maxLength={32}
              />
            </div>

            <div>
              <label htmlFor="hubDescription" className="block mb-1">
                Description
              </label>
              <textarea
                id="hubDescription"
                value={hubDescription}
                onChange={(e) => setHubDescription(e.target.value)}
                className="w-full p-2 border rounded"
                required
                minLength={10}
                maxLength={500}
              />
            </div>

            <Button type="submit" disabled={createHubMutation.isPending}>
              {createHubMutation.isPending ? "Creating..." : "Create Hub"}
            </Button>
          </form>
        </div>

        <div>
          <h3 className="text-xl font-semibold mb-2">Accessible Hubs</h3>
          {helloQuery.isLoading ? (
            <p>Loading hubs...</p>
          ) : helloQuery.error ? (
            <p className="text-red-500">Error: {helloQuery.error.message}</p>
          ) : (
            <ul className="space-y-2">
              {
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                helloQuery.data?.hubs.map((hub: any) => (
                  <li key={hub.id} className="p-3 border rounded">
                    <h4 className="font-bold">{hub.name}</h4>
                    <p className="text-sm">Role: {hub.role}</p>
                  </li>
                ))
              }
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}
