"use client";

import { Input } from "@/components/ui/input";
import { trpc } from "@/utils/trpc";
import { useState } from "react";

export function HubSearchExample() {
  const [searchTerm, setSearchTerm] = useState("");
  
  // Use tRPC query to search for hubs
  const searchQuery = trpc.hub.searchHubs.useQuery(
    { term: searchTerm },
    {
      enabled: searchTerm.length >= 2, // Only search when term is at least 2 characters
      retry: false,
    }
  );
  
  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Hub Search Example</h2>
        
        <div className="mb-6">
          <div className="flex gap-2 mb-4">
            <Input
              type="text"
              placeholder="Search hubs..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full"
            />
          </div>
          
          {searchTerm.length < 2 && (
            <p className="text-sm text-gray-500">
              Type at least 2 characters to search
            </p>
          )}
        </div>
        
        <div>
          <h3 className="text-xl font-semibold mb-2">Search Results</h3>
          {searchQuery.isLoading ? (
            <p>Loading results...</p>
          ) : searchQuery.error ? (
            <p className="text-red-500">Error: {searchQuery.error.message}</p>
          ) : !searchTerm || searchTerm.length < 2 ? (
            <p>Enter a search term to see results</p>
          ) : searchQuery.data?.hubs.length === 0 ? (
            <p>No hubs found matching &quot;{searchTerm}&quot;</p>
          ) : (
            <ul className="space-y-2">
              {searchQuery.data?.hubs.map((hub) => (
                <li key={hub.id} className="p-3 border rounded">
                  <h4 className="font-bold">{hub.name}</h4>
                  <p className="text-sm">{hub.description}</p>
                  <div className="mt-2 text-xs text-gray-500">
                    {hub.connections.length} connected servers
                    {hub.upvotes.length > 0 && ` • ${hub.upvotes.length} upvotes`}
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}
