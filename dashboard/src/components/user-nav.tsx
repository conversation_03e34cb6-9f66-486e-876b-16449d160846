"use client";

import { motion } from "motion/react";
import {
  HelpCircle,
  Home,
  LayoutDashboard,
  LogOut,
  MessageSquare,
  Shield,
  User as UserIcon
} from "lucide-react";
import type { User } from "next-auth";
import { signOut } from "next-auth/react";
import Link from "next/link";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface UserNavProps {
  user: User;
  firstPage?: { name: string; icon: React.ElementType; href: string };
}

export function UserNav({
  user,
  firstPage = { name: "Dashboard", icon: LayoutDashboard, href: "/dashboard" },
}: UserNavProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="relative h-10 w-10 rounded-full p-0 border-2 border-transparent hover:border-primary/30 transition-all duration-200 cursor-pointer"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="h-full w-full rounded-full overflow-hidden"
          >
            <Avatar className="h-9 w-9">
              <AvatarImage
                src={user.image ?? ""}
                alt={user.name ?? "User avatar"}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-indigo-500/80 to-purple-500/80 text-white">
                {(user.name ?? "U")[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </motion.div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-64 bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50 shadow-xl rounded-xl p-2 mt-1"
        align="end"
        forceMount
      >
        <div className="p-2 mb-2">
          <div className="flex items-center gap-3">
            <Avatar className="h-12 w-12 border-2 border-primary/20">
              <AvatarImage
                src={user.image ?? ""}
                alt={user.name ?? "User avatar"}
                className="object-cover"
              />
              <AvatarFallback className="bg-gradient-to-br from-indigo-500/80 to-purple-500/80 text-white text-lg">
                {(user.name ?? "U")[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <p className="text-base font-medium text-white">{user.name}</p>
              <p className="text-xs text-gray-400 truncate max-w-[160px]">
                {user.email || user.id}
              </p>
            </div>
          </div>
        </div>

        <DropdownMenuSeparator className="bg-gray-800/50 my-1" />

        <DropdownMenuGroup>
          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link href={firstPage.href} className="flex items-center w-full">
              <firstPage.icon className="h-4 w-4 mr-2 text-indigo-400" />
              {firstPage.name}
              <DropdownMenuShortcut className="text-gray-500">
                ⌘D
              </DropdownMenuShortcut>
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link href="/dashboard/hubs" className="flex items-center w-full">
              <MessageSquare className="h-4 w-4 mr-2 text-blue-400" />
              My Hubs
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link
              href="/dashboard/servers"
              className="flex items-center w-full"
            >
              <Home className="h-4 w-4 mr-2 text-green-400" />
              My Servers
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link
              href="/dashboard/moderation/reports"
              className="flex items-center w-full"
            >
              <Shield className="h-4 w-4 mr-2 text-purple-400" />
              Moderation
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-800/50 my-1" />

        <DropdownMenuGroup>
          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link
              href="/dashboard/settings"
              className="flex items-center w-full"
            >
              <UserIcon className="h-4 w-4 mr-2 text-gray-400" />
              Profile
            </Link>
          </DropdownMenuItem>

          <DropdownMenuItem
            asChild
            className="flex items-center gap-2 px-2 py-2 rounded-lg text-gray-200 hover:text-white hover:bg-gray-800/50 cursor-pointer transition-colors duration-150"
          >
            <Link href="/support" className="flex items-center w-full">
              <HelpCircle className="h-4 w-4 mr-2 text-gray-400" />
              Help & Support
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>

        <DropdownMenuSeparator className="bg-gray-800/50 my-1" />

        <DropdownMenuItem
          className="flex items-center gap-2 px-2 py-2 rounded-lg text-red-400 hover:text-red-300 hover:bg-red-950/30 cursor-pointer transition-colors duration-150"
          onClick={() => signOut({ callbackUrl: "/" })}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
