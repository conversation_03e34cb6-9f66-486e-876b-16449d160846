// home page with all these components
import { CreditsSection } from "@/components/CreditsSection";
import { FaqSection } from "@/components/FaqSection";
import { BentoFeatures } from "@/components/BentoFeatures";
import { HeroSection } from "@/components/Hero";
import { CTASection } from "@/components/CTASection";
import { OpenSourceSection } from "@/components/OpenSourceSection";
import {
  WebsiteSchema,
  SoftwareAppSchema,
  FAQSchema,
  OrganizationSchema,
} from "@/components/SchemaOrg";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "InterChat - Connect Discord Servers with Cross-Server Chat",
  description: "InterChat bridges your Discord servers, creating cross-server communication while maintaining complete moderation control. Join thousands of communities worldwide.",
  keywords: ["Discord bot", "cross-server chat", "Discord communities", "server bridge", "Discord moderation"],
  openGraph: {
    title: "InterChat - Connect Discord Servers",
    description: "Bridge Discord servers with cross-server chat, community hubs, and advanced moderation tools.",
    url: "https://interchat.tech",
    siteName: "InterChat",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "InterChat - Connect Discord Servers",
    description: "Bridge Discord servers with cross-server chat, community hubs, and advanced moderation tools.",
  },
};

export default function HomePage() {
  return (
    <>
      {/* Schema.org markup for SEO */}
      <WebsiteSchema
        name="InterChat"
        url="https://interchat.tech"
        description="InterChat bridges your Discord servers, creating cross-server communication while maintaining complete moderation control."
      />
      <SoftwareAppSchema
        name="InterChat Discord Bot"
        description="Connect Discord servers with cross-server chat, community hubs, and advanced moderation tools."
        ratingValue="4.8"
        ratingCount="256"
      />
      <OrganizationSchema />
      <FAQSchema
        faqs={[
          {
            question: "What is InterChat?",
            answer:
              "InterChat is a Discord bot that makes multi-server messaging possible. It allows you to link a channel in your server to a hub (or group chat) where you can chat with other servers that have also linked their channels.",
          },
          {
            question: "Is InterChat free to use?",
            answer:
              "Yes, InterChat is completely free to use with all core features available to everyone. We offer premium features for those who want to support the project.",
          },
          {
            question: "How do I add InterChat to my server?",
            answer:
              "You can add InterChat to your Discord server by visiting our website and clicking the 'Add to Discord' button, or by using our direct invite link.",
          },
        ]}
      />

      <main
        className="flex flex-1 flex-col justify-center text-center"
        itemScope
        itemType="https://schema.org/WebPage"
      >
        <HeroSection />
        <BentoFeatures />
        <OpenSourceSection />
        <CreditsSection />
        <FaqSection />
        <CTASection />
      </main>
    </>
  );
}
