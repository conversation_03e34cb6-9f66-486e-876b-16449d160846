import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { withAuthRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

async function handleGET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("query") || "";
    const limit = parseInt(searchParams.get("limit") || "10");

    if (!query) {
      return NextResponse.json({ users: [] });
    }

    // Search for users by name
    const users = await prisma.user.findMany({
      where: {
        name: {
          contains: query,
          mode: "insensitive",
        },
      },
      select: {
        id: true,
        name: true,
        image: true,
      },
      take: limit,

    });

    return NextResponse.json({ users });
  } catch (error) {
    console.error("Error searching users:", error);
    return NextResponse.json(
      { error: "Failed to search users" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handler
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.USERS.SEARCH,
});
