import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { Prisma } from "@/lib/generated/prisma/client";

// Define supported languages
const supportedLanguages = ["en", "hi", "es", "pt", "zh", "ru", "et"] as const;

// Schema for updating user settings
const updateUserSettingsSchema = z.object({
  mentionOnReply: z.boolean().optional(),
  locale: z.enum(supportedLanguages).optional(),
  showNsfwHubs: z.boolean().optional(),
});

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ userId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await props.params;

    // Ensure users can only view their own settings
    if (session.user.id !== userId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get user settings from database
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        mentionOnReply: true,
        locale: true,
        showNsfwHubs: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Error fetching user settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch user settings" },
      { status: 500 },
    );
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ userId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await props.params;

    // Ensure users can only update their own settings
    if (session.user.id !== userId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validationResult = updateUserSettingsSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Invalid request data",
          details: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const { mentionOnReply, locale, showNsfwHubs } = validationResult.data;

    // Only include fields that were provided in the request
    const updateData: Prisma.UserUpdateInput = {};
    if (mentionOnReply !== undefined)
      updateData.mentionOnReply = mentionOnReply;
    if (locale !== undefined) updateData.locale = locale;
    if (showNsfwHubs !== undefined) updateData.showNsfwHubs = showNsfwHubs;

    // Update user settings in database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        mentionOnReply: true,
        locale: true,
        showNsfwHubs: true,
      },
    });

    return NextResponse.json({
      user: updatedUser,
      message: "Settings updated successfully",
    });
  } catch (error) {
    console.error("Error updating user settings:", error);
    return NextResponse.json(
      { error: "Failed to update user settings" },
      { status: 500 },
    );
  }
}
