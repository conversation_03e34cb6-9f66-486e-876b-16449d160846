import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

async function handlePATCH(
  request: NextRequest,
  props: { params: Promise<{ userId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { userId } = await props.params;
    const data = await request.json();


    if (session.user.id !== userId) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Validate input data
    if (!data.image || typeof data.image !== 'string') {
      return NextResponse.json({ error: "Invalid image URL" }, { status: 400 });
    }

    // Update user's avatar in database
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { image: data.image },
      select: {
        id: true,
        name: true,
        image: true,

      },
    });

    return NextResponse.json({ user: updatedUser });
  } catch (error) {
    console.error("Error updating user:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handler
export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.USERS.UPDATE_PROFILE,
  customMessage: "Profile update rate limit exceeded. Please wait before updating your profile again.",
});
