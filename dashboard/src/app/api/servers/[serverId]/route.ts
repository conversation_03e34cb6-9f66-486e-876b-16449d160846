import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { REST } from "@discordjs/rest";
import { APIGuild, Routes } from "discord-api-types/v10";
import prisma from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ serverId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = await props.params;

    // Get bot token from environment variable
    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      return NextResponse.json(
        { error: "Bot token not configured" },
        { status: 500 },
      );
    }

    // Create REST instance
    const rest = new REST({ version: "10" }).setToken(botToken);

    // Fetch server from Discord API
    const discordServer = (await rest.get(Routes.guild(serverId))) as APIGuild;

    // Get server from database
    const dbServer = await prisma.serverData.findUnique({
      where: { id: serverId },
      select: {
        id: true,

      },
    });

    // Format the response
    const server = {
      id: discordServer.id,
      name: discordServer.name,
      icon: discordServer.icon
        ? `https://cdn.discordapp.com/icons/${discordServer.id}/${discordServer.icon}.png?size=128`
        : null,
      botAdded: !!dbServer,
    };

    return NextResponse.json({ server });
  } catch (error) {
    console.error("Error fetching server:", error);
    return NextResponse.json(
      { error: "Failed to fetch server" },
      { status: 500 },
    );
  }
}
