import { buildWhere<PERSON>lause, getSortedHubs } from "@/app/hubs/utils";
import type { FilterOptions } from "@/app/hubs/utils";
import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { SortOptions, ContentFilter, VerificationStatus, ActivityLevel } from "@/app/hubs/constants";
import { withPublicRateLimit, withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

const createHubSchema = z.object({
  name: z.string().min(3).max(32),
  description: z.string().min(10).max(500),
  shortDescription: z.string().min(10).max(100).optional(),
  private: z.boolean().default(true),
  nsfw: z.boolean().default(false),
  rules: z.array(z.string()).optional(),
});

async function handleGET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const search = searchParams.get("search") || undefined;
  const tags =
    searchParams.get("tags")?.split(",").filter(Boolean) || undefined;
  const skip = Number.parseInt(searchParams.get("skip") || "0", 10);
  const sortParam = searchParams.get("sort") || SortOptions.Trending;

  // Validate sort parameter and reject deprecated options
  const validSorts = Object.values(SortOptions);
  if (!validSorts.includes(sortParam as SortOptions)) {
    return NextResponse.json(
      { error: `Invalid sort option: ${sortParam}. Valid options are: ${validSorts.join(', ')}` },
      { status: 400 }
    );
  }

  const sort = sortParam as SortOptions;
  const moderated = searchParams.get("moderated") === "true";

  // New filter parameters
  const contentFilter = (searchParams.get("contentFilter") as ContentFilter) || ContentFilter.All;
  const verificationStatus = (searchParams.get("verificationStatus") as VerificationStatus) || VerificationStatus.All;
  const language = searchParams.get("language") || undefined;
  const region = searchParams.get("region") || undefined;
  const minServers = searchParams.get("minServers")
    ? Number.parseInt(searchParams.get("minServers") || "0", 10)
    : undefined;
  const maxServers = searchParams.get("maxServers")
    ? Number.parseInt(searchParams.get("maxServers") || "0", 10)
    : undefined;
  const activityLevels = searchParams.get("activityLevels")?.split(",").filter(Boolean) as ActivityLevel[] || undefined;

  try {
    // If moderated=true, return hubs where the user is an owner or moderator
    if (moderated) {
      const session = await auth();
      if (!session?.user) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const userId = session.user.id;

      // Get hubs where the user is an owner or moderator
      const hubs = await prisma.hub.findMany({
        where: {
          OR: [{ ownerId: userId }, { moderators: { some: { userId } } }],
        },
        select: {
          id: true,
          name: true,
          description: true,
          shortDescription: true,
          iconUrl: true,
          private: true,
          ownerId: true,
          moderators: {
            select: {
              userId: true,
              role: true,
            },
          },
        },
        orderBy: { name: "asc" },
      });

      // Format the response
      const formattedHubs = hubs.map((hub) => ({
        id: hub.id,
        name: hub.name,
        description: hub.description,
        shortDescription: hub.shortDescription,
        iconUrl: hub.iconUrl,
        private: hub.private,
        isOwner: hub.ownerId === userId,
        isModerator: hub.moderators.some((mod) => mod.userId === userId),
        role:
          hub.ownerId === userId
            ? "OWNER"
            : hub.moderators.find((mod) => mod.userId === userId)?.role || null,
      }));

      return NextResponse.json({ hubs: formattedHubs });
    }

    // For the new blur approach, we always show all content
    // The frontend will handle blurring NSFW content based on user preferences
    let effectiveContentFilter = contentFilter;
    // Only filter if user explicitly requests SFW or NSFW only
    if (contentFilter === ContentFilter.SFW) {
      effectiveContentFilter = ContentFilter.SFW;
    } else if (contentFilter === ContentFilter.NSFW) {
      effectiveContentFilter = ContentFilter.NSFW;
    } else {
      // For "All" content filter, show everything and let frontend handle blurring
      effectiveContentFilter = ContentFilter.All;
    }

    // Regular hub search with enhanced filters
    const filterOptions: FilterOptions = {
      search,
      tags,
      contentFilter: effectiveContentFilter,
      verificationStatus,
      language,
      region,
      minServers,
      maxServers,
      activityLevels
    };

    const whereClause = buildWhereClause(filterOptions);
    const result = await getSortedHubs(whereClause, skip, sort, minServers, maxServers, activityLevels);

    return NextResponse.json({
      hubs: result.hubs,
      pagination: {
        totalItems: result.totalCount,
        hasMore: result.hubs.length > 0 && skip + result.hubs.length < result.totalCount,
      },
    });
  } catch (error) {
    console.error("Error fetching hubs:", error);
    return NextResponse.json(
      { error: "Failed to fetch hubs" },
      { status: 500 }
    );
  }
}

async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validation = createHubSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 }
      );
    }

    const { name, description, shortDescription, private: isPrivate, nsfw, rules } = validation.data;

    // Check if the hub name is already taken (case insensitive)
    const existingHub = await prisma.hub.findFirst({
      where: {
        name: { equals: name, mode: "insensitive" },
      },

    });

    if (existingHub) {
      return NextResponse.json(
        { error: "Hub name already taken" },
        { status: 400 }
      );
    }

    // Create the hub
    const hub = await prisma.hub.create({
      data: {
        name,
        description,
        shortDescription,
        ownerId: session.user.id,
        iconUrl: `https://api.dicebear.com/7.x/shapes/svg?seed=${encodeURIComponent(
          name
        )}`,
        private: isPrivate,
        nsfw: nsfw || false,
        settings: 0,
        rules: rules || [],
      },
      select: {
        id: true,
        name: true,
        description: true,
        shortDescription: true,
        ownerId: true,
        iconUrl: true,
        bannerUrl: true,
        private: true,
        nsfw: true,
        locked: true,
        settings: true,
        createdAt: true,
        updatedAt: true,
        rules: true,

        moderators: {
          select: {
            id: true,
            userId: true,
            role: true,
            user: {
              select: {
                id: true,
                name: true,
                image: true,

              },
            },
          },
        },
      },
    });

    return NextResponse.json({ hub }, { status: 201 });
  } catch (error) {
    console.error("Error creating hub:", error);
    return NextResponse.json(
      { error: "Failed to create hub" },
      { status: 500 }
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withPublicRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.LIST,
});

export const POST = withStrictRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.CREATE,
  customMessage: "Hub creation rate limit exceeded. Please wait before creating another hub.",
});
