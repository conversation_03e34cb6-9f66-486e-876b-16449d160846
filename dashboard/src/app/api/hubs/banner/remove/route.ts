import { auth } from "@/auth";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { unlink } from "fs/promises";
import { join } from "path";
import { existsSync } from "fs";

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await request.json();

    if (!hubId) {
      return NextResponse.json(
        { error: "Missing hub ID" },
        { status: 400 }
      );
    }

    // Check permissions
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get current hub with banner URL
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: { id: true, bannerUrl: true },
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Remove banner from database
    await prisma.hub.update({
      where: { id: hubId },
      data: { bannerUrl: null },
    });

    // Remove file from filesystem if it exists
    if (hub.bannerUrl && hub.bannerUrl.startsWith("/uploads/banners/")) {
      const fileName = hub.bannerUrl.replace("/uploads/banners/", "");
      const filePath = join(process.cwd(), "public", "uploads", "banners", fileName);
      
      if (existsSync(filePath)) {
        try {
          await unlink(filePath);
        } catch (error) {
          console.error("Error deleting banner file:", error);
          // Continue even if file deletion fails
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: "Banner removed successfully",
    });
  } catch (error) {
    console.error("Error removing banner:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
