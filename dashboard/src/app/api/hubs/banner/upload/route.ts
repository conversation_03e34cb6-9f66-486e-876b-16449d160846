import { NextResponse } from "next/server";

/**
 * Legacy banner upload route - now redirects to UploadThing
 * This route is kept for backward compatibility but should not be used
 * Use the UploadThing integration instead: /api/uploadthing
 */
export async function POST() {
  return NextResponse.json(
    {
      error: "This endpoint has been deprecated. Please use the UploadThing integration.",
      redirectTo: "/api/uploadthing"
    },
    { status: 410 } // Gone
  );
}
