import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { isUserBlacklisted } from "@/lib/blacklist";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ code: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { code } = await props.params;

    // Find the invite
    const invite = await prisma.hubInvite.findUnique({
      where: { code },
      include: {
        hub: {
          select: {
            id: true,
            name: true,
            description: true,
            iconUrl: true,
            private: true,
          },
        },
      },
      
    });

    if (!invite) {
      return NextResponse.json(
        { error: "Invalid invite code" },
        { status: 404 },
      );
    }

    // Check if the invite is expired
    if (invite.expires && new Date(invite.expires) < new Date()) {
      return NextResponse.json(
        { error: "Invite code expired" },
        { status: 400 },
      );
    }

    // Check if the user is blacklisted from the hub
    const userBlacklisted = await isUserBlacklisted(
      session.user.id,
      invite.hub.id,
    );
    if (userBlacklisted) {
      return NextResponse.json(
        { error: "You are blacklisted from this hub" },
        { status: 403 },
      );
    }

    // Return the hub details
    return NextResponse.json({ hub: invite.hub });
  } catch (error) {
    console.error("Error validating invite code:", error);
    return NextResponse.json(
      { error: "Failed to validate invite code" },
      { status: 500 },
    );
  }
}
