import { auth } from "@/auth";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PermissionLevel } from "@/lib/constants";

// Schema for adding a member
const addMemberSchema = z.object({
  userId: z.string(),
  role: z.enum(["MODERATOR", "MANAGER"]),
});

// Get all members of a hub
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to view this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel === PermissionLevel.NONE) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub with owner and moderators
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        moderators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Format the response
    const members = {
      owner: hub.owner,
      moderators: hub.moderators.map((mod) => ({
        id: mod.id,
        userId: mod.userId,
        role: mod.role,
        user: mod.user,
      })),
    };

    return NextResponse.json({ members });
  } catch (error) {
    console.error("Error fetching hub members:", error);
    return NextResponse.json(
      { error: "Failed to fetch hub members" },
      { status: 500 },
    );
  }
}

// Add a new member to the hub
export async function POST(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to manage members
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        ownerId: true,

      },
    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = addMemberSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { userId, role } = validation.data;

    // Check if the user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,

      },
    });

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Check if the user is already the owner
    if (hub.ownerId === userId) {
      return NextResponse.json(
        { error: "User is already the owner of this hub" },
        { status: 400 },
      );
    }

    // Check if the user is already a moderator
    const existingModerator = await prisma.hubModerator.findUnique({
      where: {
        hubId_userId: {
          hubId,
          userId,
        },
      },
      select: {
        id: true,

      },
    });

    if (existingModerator) {
      return NextResponse.json(
        { error: "User is already a moderator of this hub" },
        { status: 400 },
      );
    }

    // Add the user as a moderator
    const moderator = await prisma.hubModerator.create({
      data: {
        hubId,
        userId,
        role,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({ moderator }, { status: 201 });
  } catch (error) {
    console.error("Error adding hub member:", error);
    return NextResponse.json(
      { error: "Failed to add hub member" },
      { status: 500 },
    );
  }
}
