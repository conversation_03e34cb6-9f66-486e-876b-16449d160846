import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { withAuthRateLimit, withStrictRateLimit, withCriticalRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for updating a hub
const updateHubSchema = z.object({
  name: z.string().min(3).max(32).optional(),
  description: z.string().min(10).max(500).optional(),
  private: z.boolean().optional(),
  nsfw: z.boolean().optional(),
  welcomeMessage: z.string().max(1000).nullable().optional(),
  rules: z.array(z.string()).optional(),
  language: z.string().min(2).max(10).optional(),
});

async function handleGET(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Get the hub first to check if it's public
    const hubBasic = await prisma.hub.findUnique({
      where: { id: hubId },
      select: {
        private: true,
      },

    });

    if (!hubBasic) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Only check permissions for private hubs
    if (hubBasic.private) {
      const permissionLevel = await getUserHubPermission(
        session.user.id,
        hubId,
      );
      if (permissionLevel === PermissionLevel.NONE) {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
    }

    // Get the hub
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },
      include: {
        owner: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        moderators: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Add isOwner flag to the response
    const isOwner = hub.owner?.id === session.user.id;
    const hubWithOwnerFlag = {
      ...hub,
      isOwner: !!isOwner,
    };

    return NextResponse.json({ hub: hubWithOwnerFlag });
  } catch (error) {
    console.error("Error fetching hub:", error);
    return NextResponse.json({ error: "Failed to fetch hub" }, { status: 500 });
  }
}

async function handlePATCH(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to edit this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateHubSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const {
      name,
      description,
      private: isPrivate,
      nsfw,
      welcomeMessage,
      rules,
      language,
    } = validation.data;

    // Check if the name is already taken (if it's being changed)
    if (name && name.toLowerCase() !== hub.name.toLowerCase()) {
      const existingHub = await prisma.hub.findFirst({
        where: {
          name: { equals: name, mode: "insensitive" },
          id: { not: hubId }, // Exclude current hub
        },

      });

      if (existingHub) {
        return NextResponse.json(
          { error: "Hub name already taken" },
          { status: 400 },
        );
      }
    }

    // Update the hub
    const updatedHub = await prisma.hub.update({
      where: { id: hubId },
      data: {
        ...(name && { name }),
        ...(description && { description }),
        ...(isPrivate !== undefined && { private: isPrivate }),
        ...(nsfw !== undefined && { nsfw }),
        ...(welcomeMessage !== undefined && { welcomeMessage }),
        ...(rules && { rules }),
        ...(language && { language }),
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({ hub: updatedHub });
  } catch (error) {
    console.error("Error updating hub:", error);
    return NextResponse.json(
      { error: "Failed to update hub" },
      { status: 500 },
    );
  }
}

// Schema for deleting a hub
const deleteHubSchema = z.object({
  confirmName: z.string(),
});

async function handleDELETE(
  request: NextRequest,
  props: { params: Promise<{ hubId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId } = await props.params;

    // Check if the user has permission to delete this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.OWNER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the hub
    const hub = await prisma.hub.findUnique({
      where: { id: hubId },

    });

    if (!hub) {
      return NextResponse.json({ error: "Hub not found" }, { status: 404 });
    }
    if (hub.ownerId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    let confirmName = "";
    try {
      const body = await request.json();
      const validation = deleteHubSchema.safeParse(body);
      if (validation.success) {
        confirmName = validation.data.confirmName;
      }
    } catch {
      // If no body is provided, continue without confirmation
      console.warn("No confirmation provided for hub deletion");
    }

    // Verify the confirmation name matches the hub name
    if (confirmName !== hub.name) {
      return NextResponse.json(
        { error: "Confirmation name does not match hub name" },
        { status: 400 },
      );
    }

    // hell
    await prisma.hubModerator.deleteMany({ where: { hubId } });
    await prisma.connection.deleteMany({ where: { hubId } });
    await prisma.hubReview.deleteMany({ where: { hubId } });
    await prisma.hubUpvote.deleteMany({ where: { hubId } });
    await prisma.hubRulesAcceptance.deleteMany({ where: { hubId } });
    await prisma.hubInvite.deleteMany({ where: { hubId } });
    await prisma.hubLogConfig.deleteMany({ where: { hubId } });
    await prisma.blockWord.deleteMany({ where: { hubId } });
    await prisma.appeal.deleteMany({ where: { infraction: { hubId } } });
    await prisma.infraction.deleteMany({ where: { hubId } });
    await prisma.hub.delete({ where: { id: hubId } });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting hub:", error);
    return NextResponse.json(
      { error: "Failed to delete hub" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.GET_DETAILS,
});

export const PATCH = withStrictRateLimit(handlePATCH, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.UPDATE,
  customMessage: "Hub update rate limit exceeded. Please wait before updating the hub again.",
});

export const DELETE = withCriticalRateLimit(handleDELETE, {
  tier: ENDPOINT_RATE_LIMITS.HUBS.DELETE,
  customMessage: "Hub deletion is heavily rate limited. Please wait before attempting to delete another hub.",
});
