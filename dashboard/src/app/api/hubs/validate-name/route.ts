import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const name = request.nextUrl.searchParams.get("name");
    if (!name) {
      return NextResponse.json(
        { error: "Name parameter is required" },
        { status: 400 },
      );
    }

    // Check if a hub with this name already exists (both public and private)
    const existingHub = await prisma.hub.findFirst({
      where: {
        name: { equals: name, mode: "insensitive" },
      },
      select: {
        id: true,
      },
    });

    return NextResponse.json({
      available: !existingHub,
    });
  } catch (error) {
    console.error("Error validating hub name:", error);
    return NextResponse.json(
      { error: "Failed to validate hub name" },
      { status: 500 },
    );
  }
}
