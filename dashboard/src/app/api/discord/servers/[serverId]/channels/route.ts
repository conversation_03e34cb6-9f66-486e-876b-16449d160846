import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { REST } from "@discordjs/rest";
import {
  APIChannel,
  APIGuildTextChannel,
  ChannelType,
  GuildTextChannelType,
  Routes,
} from "discord-api-types/v10";
import { NextRequest, NextResponse } from "next/server";

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ serverId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { serverId } = await props.params;

    // Get bot token from environment variable
    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      console.error("Discord bot token not configured");
      return NextResponse.json(
        { error: "Service configuration error" },
        { status: 500 },
      );
    }

    // Create REST instance
    const rest = new REST({ version: "10" }).setToken(botToken);

    // Fetch channels from Discord API
    const discordChannels = (await rest.get(
      Routes.guildChannels(serverId),
    )) as APIChannel[];

    // Get all existing connections to filter out already connected channels
    // We need to check all connections, not just for this server, because a channel can only be connected to one hub
    const existingConnections = await prisma.connection.findMany({
      where: {
        connected: true,
      },
      select: {
        channelId: true,
      },

    });

    const connectedChannelIds = existingConnections.map(
      (conn) => conn.channelId,
    );

    // Get query parameter for hubId if it exists
    const url = new URL(request.url);
    const hubIdParam = url.searchParams.get("hubId");

    // If hubId is provided, also get existing connections for this hub and server combination
    let existingHubServerConnection = null;
    if (hubIdParam) {
      existingHubServerConnection = await prisma.connection.findFirst({
        where: {
          serverId: serverId,
          hubId: hubIdParam,
          connected: true,
        },

      });
    }

    // Process and filter channels
    const processedChannels = discordChannels
      // Filter out categories, voice channels, and already connected channels
      .filter((channel) => {
        const isTextChannel = channel.type === ChannelType.GuildText;
        const isThread =
          channel.type === ChannelType.PublicThread ||
          channel.type === ChannelType.PrivateThread ||
          channel.type === ChannelType.AnnouncementThread;

        const isEligible = isTextChannel || isThread;
        const isNotConnected = !connectedChannelIds.includes(channel.id);

        // If hubId is provided and there's already a connection between this server and hub,
        // don't allow adding more channels (one server can only connect to a hub once)
        const hubServerAlreadyConnected =
          hubIdParam && existingHubServerConnection !== null;

        return isEligible && isNotConnected && !hubServerAlreadyConnected;
      })
      // Map to a simpler structure
      .map((channel) => {
        const isThread =
          channel.type === ChannelType.PublicThread ||
          channel.type === ChannelType.PrivateThread ||
          channel.type === ChannelType.AnnouncementThread;

        // Find parent channel for threads
        let parentChannel = null;
        if (isThread && channel.parent_id) {
          parentChannel = discordChannels.find(
            (c) => c.id === channel.parent_id,
          );
        }

        return {
          id: channel.id,
          name: channel.name,
          type: channel.type,
          parentId:
            channel.type === ChannelType.PublicThread ||
            channel.type === ChannelType.PrivateThread ||
            channel.type === ChannelType.AnnouncementThread
              ? channel.parent_id
              : null,
          parentName: parentChannel ? parentChannel.name : null,
          isThread: isThread,
          isPrivateThread: channel.type === ChannelType.PrivateThread,
        };
      })
      // Sort by position and name
      .sort((a, b) => {
        // Sort threads after their parent channels
        if (a.isThread && !b.isThread) return 1;
        if (!a.isThread && b.isThread) return -1;

        // If both are threads with the same parent, sort by name
        if (
          a.name &&
          b.name &&
          a.isThread &&
          b.isThread &&
          a.parentId === b.parentId
        ) {
          return a.name.localeCompare(b.name);
        }

        return (
          (a as unknown as APIGuildTextChannel<GuildTextChannelType>).position -
          (b as unknown as APIGuildTextChannel<GuildTextChannelType>).position
        );
      });

    return NextResponse.json({ channels: processedChannels });
  } catch (error) {
    console.error("Error fetching Discord channels:", error);
    return NextResponse.json(
      { error: "Failed to fetch Discord channels" },
      { status: 500 },
    );
  }
}
