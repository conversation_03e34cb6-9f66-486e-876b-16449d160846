import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import prisma from "@/lib/prisma";
import { z } from "zod";
import type { AppealStatus, Prisma } from "@/lib/generated/prisma/client";
import { withAuthRateLimit, withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for creating an appeal
const createAppealSchema = z.object({
  infractionId: z.string(),
  reason: z.string().min(10).max(1000),
});

// Helper function to get hubs that the user has access to
async function getUserAccessibleHubs(userId: string) {
  // Get hubs where user is owner
  const ownedHubs = await prisma.hub.findMany({
    where: { ownerId: userId },
    select: { id: true },

  });

  // Get hubs where user is a moderator or manager
  const moderatedHubs = await prisma.hubModerator.findMany({
    where: { userId },
    select: { hubId: true },

  });

  // Combine the hub IDs
  return [
    ...ownedHubs.map((hub) => ({ id: hub.id })),
    ...moderatedHubs.map((mod) => ({ id: mod.hubId })),
  ];
}

// GET all appeals with filtering
async function handleGET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get("status") as AppealStatus | undefined;
    const userId = searchParams.get("userId");
    const infractionId = searchParams.get("infractionId");
    const hubId = searchParams.get("hubId");
    const myAppeals = searchParams.get("myAppeals") === "true";

    // Pagination parameters
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // Build the where clause
    const whereClause: Prisma.AppealWhereInput = {};

    if (status) {
      whereClause.status = status;
    }

    // If myAppeals is true, filter by the current user's ID
    if (myAppeals) {
      whereClause.userId = session.user.id;
    } else {
      // Get accessible hubs for the user
      const accessibleHubs = await getUserAccessibleHubs(session.user.id);

      if (accessibleHubs.length === 0) {
        // User has no moderator access to any hubs
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }

      // Always restrict to accessible hubs for moderation view
      whereClause.infraction = {
        hubId: { in: accessibleHubs.map((hub) => hub.id) },
      };

      // Apply additional filters if provided
      if (userId) {
        whereClause.userId = userId;
      }

      if (hubId) {
        // Verify the user has access to this specific hub
        if (!accessibleHubs.some((hub) => hub.id === hubId)) {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }
        // Override the hubId filter to be more specific
        whereClause.infraction = {
          hubId: hubId,
        };
      }
    }

    if (infractionId) {
      whereClause.infractionId = infractionId;
    }

    // Count total matching appeals for pagination
    const total = await prisma.appeal.count({
      where: whereClause,
    });

    // Fetch appeals with related data
    const appeals = await prisma.appeal.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        infraction: {
          include: {
            hub: {
              select: {
                id: true,
                name: true,
                iconUrl: true,
              },
            },
            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,

    });

    return NextResponse.json({
      appeals,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (error) {
    console.error("Error fetching appeals:", error);
    return NextResponse.json(
      { error: "Failed to fetch appeals" },
      { status: 500 },
    );
  }
}

// POST (create) a new appeal
async function handlePOST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = createAppealSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { infractionId, reason } = validation.data;

    // Check if the infraction exists
    const infraction = await prisma.infraction.findUnique({
      where: { id: infractionId },

    });

    if (!infraction) {
      return NextResponse.json(
        { error: "Infraction not found" },
        { status: 404 },
      );
    }

    // Check if the user is the one who received the infraction
    if (infraction.userId && infraction.userId !== session.user.id) {
      return NextResponse.json(
        { error: "You can only appeal your own infractions" },
        { status: 403 },
      );
    }

    // Check if the infraction is already appealed
    if (infraction.status === "APPEALED") {
      return NextResponse.json(
        { error: "This infraction has already been appealed" },
        { status: 400 },
      );
    }

    // Check if there's already a pending appeal for this infraction
    const existingAppeal = await prisma.appeal.findFirst({
      where: {
        infractionId,
        status: "PENDING",
      },

    });

    if (existingAppeal) {
      return NextResponse.json(
        { error: "There is already a pending appeal for this infraction" },
        { status: 400 },
      );
    }

    // Create the appeal
    const appeal = await prisma.appeal.create({
      data: {
        infractionId,
        userId: session.user.id,
        reason,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
        infraction: {
          include: {
            hub: {
              select: {
                id: true,
                name: true,
                iconUrl: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json({ appeal });
  } catch (error) {
    console.error("Error creating appeal:", error);
    return NextResponse.json(
      { error: "Failed to create appeal" },
      { status: 500 },
    );
  }
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
  tier: ENDPOINT_RATE_LIMITS.APPEALS.LIST,
});

export const POST = withStrictRateLimit(handlePOST, {
  tier: ENDPOINT_RATE_LIMITS.APPEALS.CREATE,
  customMessage: "Appeal creation rate limit exceeded. You can only create a limited number of appeals per day.",
});
