"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { formatDistanceToNow } from "date-fns";
import {
  AlertTriangle,
  ArrowLeft,
  Ban,
  Check,
  Clock,
  Home,
  HomeIcon,
  MessageSquare,
  Shield,
  User,
  X
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useCallback, useEffect, useState } from "react";

interface InfractionUser {
  id: string;
  name: string | null;
  image: string | null;
}

interface InfractionHub {
  id: string;
  name: string;
  iconUrl: string;
}

interface Infraction {
  id: string;
  hubId: string;
  type: string;
  status: string;
  moderatorId: string;
  reason: string;
  expiresAt: string | null;
  appealedAt: string | null;
  appealedBy: string | null;
  notified: boolean;
  userId: string | null;
  serverId: string | null;
  serverName: string | null;
  createdAt: string;
  updatedAt: string;
  hub: InfractionHub;
  moderator: InfractionUser | null;
  user: InfractionUser | null;
}

interface Appeal {
  id: string;
  infractionId: string;
  userId: string;
  reason: string;
  status: string;
  createdAt: string;
  updatedAt: string;
  user: InfractionUser;
}

export default function InfractionDetailPage() {
  const params = useParams();
  const infractionId = params.infractionId as string;
  const { toast } = useToast();

  const [infraction, setInfraction] = useState<Infraction | null>(null);
  const [appeals, setAppeals] = useState<Appeal[]>([]);
  const [loading, setLoading] = useState(true);
  const [appealsLoading, setAppealsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [appealsCount, setAppealsCount] = useState(0);
  const [showAppeals, setShowAppeals] = useState(false);

  // Fetch infraction data
  const fetchInfraction = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/infractions/${infractionId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch infraction");
      }

      const data = await response.json();
      setInfraction(data.infraction);
    } catch (error) {
      console.error("Error fetching infraction:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch infraction"
      );
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to fetch infraction",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [infractionId, toast]);

  // Fetch appeals for this infraction
  const fetchAppeals = useCallback(async () => {
    if (!infractionId) return;

    try {
      setAppealsLoading(true);
      const response = await fetch(`/api/appeals?infractionId=${infractionId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to fetch appeals");
      }

      const data = await response.json();
      setAppeals(data.appeals);
      setAppealsCount(data.total);
    } catch (error) {
      console.error("Error fetching appeals:", error);
      // Don't show toast for appeals loading error
    } finally {
      setAppealsLoading(false);
    }
  }, [infractionId]);

  useEffect(() => {
    fetchInfraction();
  }, [fetchInfraction]);

  useEffect(() => {
    fetchAppeals();
  }, [fetchAppeals]);

  const getInfractionTypeIcon = (type: string) => {
    switch (type) {
      case "BLACKLIST":
        return <Ban className="h-4 w-4 text-red-500" />;
      case "WARNING":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const getInfractionStatusBadge = (status: string) => {
    switch (status) {
      case "ACTIVE":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/10 text-green-500 border-green-500/20"
          >
            Active
          </Badge>
        );
      case "REVOKED":
        return (
          <Badge
            variant="outline"
            className="bg-gray-500/10 text-gray-400 border-gray-500/20"
          >
            Revoked
          </Badge>
        );
      case "APPEALED":
        return (
          <Badge
            variant="outline"
            className="bg-blue-500/10 text-blue-500 border-blue-500/20"
          >
            Appealed
          </Badge>
        );
      default:
        return null;
    }
  };

  const getAppealStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "ACCEPTED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/10 text-green-500 border-green-500/20"
          >
            <Check className="h-3 w-3 mr-1" />
            Accepted
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/10 text-red-500 border-red-500/20"
          >
            <X className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-24" />
          <Skeleton className="h-8 w-48" />
        </div>
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardHeader>
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start gap-4">
              <Skeleton className="h-12 w-12 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-5 w-48 mb-2" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4 mt-1" />
              </div>
              <Skeleton className="h-8 w-24" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              {Array.from({ length: 4 }).map((_, i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error || !infraction) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Dashboard
            </Link>
          </Button>
        </div>
        <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertTriangle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium mb-2">
              Error Loading Infraction
            </h3>
            <p className="text-gray-400 mb-4">
              {error || "Infraction not found"}
            </p>
            <Button onClick={fetchInfraction}>Try Again</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const isUserInfraction = !!infraction.userId;
  const targetName = isUserInfraction
    ? infraction.user?.name || "Unknown User"
    : infraction.serverName || "Unknown Server";

  const expiresAt = infraction.expiresAt
    ? new Date(infraction.expiresAt)
    : null;

  const expiresIn = expiresAt
    ? new Date() > expiresAt
      ? "Expired"
      : formatDistanceToNow(expiresAt, { addSuffix: true })
    : "Never";

  const createdAt = new Date(infraction.createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "numeric",
    minute: "numeric",
  });

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full sm:w-auto"
            asChild
          >
            <Link href={`/dashboard/hubs/${infraction.hubId}/infractions`}>
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to Infractions
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Infraction Details
          </h1>
        </div>
        {infraction.status === "ACTIVE" && (
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              size="sm"
              className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-blue-400 transition-all w-full sm:w-auto"
              asChild
            >
              <Link
                href={`/dashboard/moderation/blacklist/extend/${infraction.id}`}
              >
                <Clock className="h-4 w-4 mr-1" />
                Extend
              </Link>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="border-red-700/30 bg-red-950/20 text-red-400 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 transition-all w-full sm:w-auto"
              asChild
            >
              <Link
                href={`/dashboard/moderation/blacklist/remove/${infraction.id}`}
              >
                <Shield className="h-4 w-4 mr-1" />
                Remove
              </Link>
            </Button>
          </div>
        )}
      </div>
      {/* Infraction Details */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-3 border-b border-gray-800/50">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <div>
              <CardTitle className="text-lg">
                Infraction #{infraction.id}
              </CardTitle>
              <CardDescription>
                {infraction.type === "BLACKLIST" ? "Blacklist" : "Warning"}{" "}
                issued in {infraction.hub.name}
              </CardDescription>
            </div>
            <div>{getInfractionStatusBadge(infraction.status)}</div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Target Information */}
          <div className="flex items-start gap-4 p-4 rounded-md bg-gray-900/50 border border-gray-800/50">
            <div className="relative">
              {isUserInfraction ? (
                <Image
                  src={infraction.user?.image || "/pfp1.png"}
                  alt={targetName}
                  width={48}
                  height={48}
                  className="rounded-full border-2 border-blue-500/20"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center border-2 border-green-500/20">
                  <HomeIcon className="h-6 w-6 text-gray-300" />
                </div>
              )}
              <div className="absolute -bottom-1 -right-1 bg-gray-950 rounded-full p-0.5 border border-gray-700 shadow-md">
                {isUserInfraction ? (
                  <User className="h-3.5 w-3.5 text-blue-400" />
                ) : (
                  <Home className="h-3.5 w-3.5 text-green-400" />
                )}
              </div>
            </div>
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-1 gap-2">
                <div>
                  <h3 className="font-medium">{targetName}</h3>
                  <p
                    className="text-sm text-gray-400 truncate max-w-[250px] sm:max-w-[300px] md:max-w-none"
                    title={
                      (isUserInfraction
                        ? infraction.userId
                        : infraction.serverId) || undefined
                    }
                  >
                    {isUserInfraction ? "User ID: " : "Server ID: "}
                    {isUserInfraction ? infraction.userId : infraction.serverId}
                  </p>
                </div>
                <div className="flex items-center gap-1">
                  {getInfractionTypeIcon(infraction.type)}
                  <span className="text-sm">
                    {infraction.type === "BLACKLIST" ? "Blacklist" : "Warning"}
                  </span>
                </div>
              </div>
              <div className="mt-3 p-3 rounded-md bg-gray-900/30 border border-gray-800/50">
                <h4 className="text-sm font-medium mb-1 text-gray-400">
                  Reason:
                </h4>
                <p className="text-sm text-gray-300">{infraction.reason}</p>
              </div>
            </div>
          </div>

          {/* Infraction Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 rounded-md bg-gray-900/30 border border-gray-800/50">
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-400">
                  Created By
                </h4>
                <div className="flex items-center gap-2 mt-1">
                  <Image
                    src={
                      infraction.moderator?.image ||
                      "/images/default-avatar.png"
                    }
                    alt={infraction.moderator?.name || "Moderator"}
                    width={24}
                    height={24}
                    className="rounded-full border border-gray-700/50"
                  />
                  <span>{infraction.moderator?.name || "Unknown"}</span>
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-400">
                  Created At
                </h4>
                <p>{createdAt}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <h4 className="text-sm font-medium text-gray-400">Expires</h4>
                <p>{expiresIn}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-400">Status</h4>
                <p>
                  {infraction.status.charAt(0) +
                    infraction.status.slice(1).toLowerCase()}
                </p>
              </div>
            </div>
          </div>

          {/* Appeals Section */}
          <div className="pt-4 border-t border-gray-800/50">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-4 gap-2">
              <h3 className="text-md font-medium">Appeals</h3>
              {appealsCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full sm:w-auto"
                  onClick={() => setShowAppeals(!showAppeals)}
                >
                  {showAppeals
                    ? "Hide Appeals"
                    : `View ${appealsCount} Appeal${
                        appealsCount !== 1 ? "s" : ""
                      }`}
                </Button>
              )}
            </div>

            {appealsLoading ? (
              <div className="space-y-3">
                <Skeleton className="h-24 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : appealsCount === 0 ? (
              <div className="text-center py-6 bg-gray-900/30 rounded-md border border-gray-800/50">
                <MessageSquare className="h-8 w-8 mx-auto text-gray-500 mb-2" />
                <p className="text-gray-400">
                  No appeals have been submitted for this infraction.
                </p>
              </div>
            ) : showAppeals ? (
              <div className="space-y-4">
                {appeals.map((appeal) => (
                  <div
                    key={appeal.id}
                    className="p-4 rounded-md bg-gray-900/30 border border-gray-800/50"
                  >
                    <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-3 gap-2">
                      <div className="flex items-center gap-3">
                        <Image
                          src={
                            appeal.user.image || "/images/default-avatar.png"
                          }
                          alt={appeal.user.name || "User"}
                          width={32}
                          height={32}
                          className="rounded-full border-2 border-blue-500/20"
                        />
                        <div>
                          <div className="font-medium">
                            {appeal.user.name || "Unknown User"}
                          </div>
                          <div className="text-xs text-gray-400">
                            Submitted{" "}
                            {formatDistanceToNow(new Date(appeal.createdAt), {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                      <div>{getAppealStatusBadge(appeal.status)}</div>
                    </div>
                    <div className="mb-2 p-3 rounded-md bg-gray-900/50 border border-gray-800/50">
                      <h4 className="text-sm font-medium text-gray-400 mb-1">
                        Appeal Reason:
                      </h4>
                      <p className="text-sm text-gray-300">{appeal.reason}</p>
                    </div>
                    <div className="flex justify-end">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full sm:w-auto"
                        asChild
                      >
                        <Link
                          href={`/dashboard/appeals?infractionId=${infraction.id}`}
                        >
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    className="w-full sm:w-auto"
                    asChild
                  >
                    <Link
                      href={`/dashboard/appeals?infractionId=${infraction.id}`}
                    >
                      View All Appeals
                    </Link>
                  </Button>
                </div>
              </div>
            ) : (
              <div className="flex justify-center">
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                  onClick={() => setShowAppeals(true)}
                >
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Show {appealsCount} Appeal{appealsCount !== 1 ? "s" : ""}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
