import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { notFound, redirect } from "next/navigation";

interface RemoveBlacklistLayoutProps {
  children: React.ReactNode;
  params: Promise<{ infractionId: string }>;
}

export default async function RemoveBlacklistLayout({
  children,
  params,
}: RemoveBlacklistLayoutProps) {
  const infractionId = (await params)?.infractionId;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/moderation/blacklist/remove/${infractionId}`);
  }

  // Get the infraction to check the hubId
  const infraction = await prisma.infraction.findUnique({
    where: { id: infractionId },
    select: { hubId: true },
    
  });

  if (!infraction) {
    notFound();
  }

  // Check if the user has permission to modify this infraction
  const permissionLevel = await getUserHubPermission(session.user.id, infraction.hubId);

  // User must have at least moderator permissions to remove from blacklist
  if (permissionLevel < PermissionLevel.MODERATOR) {
    notFound();
  }

  return <>{children}</>;
}
