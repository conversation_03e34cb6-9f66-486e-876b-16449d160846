"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { formatDistanceToNow } from "date-fns";
import { ArrowLeft, Home, Loader2, Shield } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { FullInfraction } from "../../page";

export default function RemoveBlacklistPage() {
  const { infractionId } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [infraction, setInfraction] = useState<FullInfraction | null>(null);
  const [reason, setReason] = useState<string>("");

  const { toast } = useToast();
  const router = useRouter();

  // Fetch infraction data
  useEffect(() => {
    const fetchInfraction = async () => {
      try {
        setIsLoading(true);

        const response = await fetch(`/api/infractions/${infractionId}`);

        if (!response.ok) {
          throw new Error("Failed to fetch infraction data");
        }

        const data = await response.json();
        setInfraction(data.infraction);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching infraction:", error);
        toast({
          title: "Error",
          description: "Failed to load infraction data. Please try again.",
          variant: "destructive",
        });
        router.push("/dashboard/moderation/blacklist");
      }
    };

    fetchInfraction();
  }, [infractionId, toast, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!reason) {
      toast({
        title: "Error",
        description: "Please provide a reason for removing from the blacklist.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/infractions/${infractionId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: "REVOKED",
          reason: `[REVOKED] ${reason}`,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to remove from blacklist");
      }

      toast({
        title: "Success",
        description: "Successfully removed from blacklist.",
      });

      router.push("/dashboard/moderation/blacklist");
    } catch (error) {
      console.error("Error removing from blacklist:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to remove from blacklist",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  const isUserBlacklist = !!infraction?.userId;
  const blacklistedOn = formatDistanceToNow(
    infraction?.createdAt || new Date(),
    {
      addSuffix: true,
    },
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center">
        <Button variant="ghost" size="sm" className="mr-2" asChild>
          <Link href="/dashboard/moderation/blacklist">
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Link>
        </Button>
        <h1 className="text-2xl font-bold tracking-tight">
          Remove from Blacklist
        </h1>
      </div>
      <Card className="border-gray-800 bg-[#0f1117]">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Image
                src={
                  infraction?.hub.iconUrl ||
                  "https://api.dicebear.com/7.x/shapes/svg?seed=hub"
                }
                alt={infraction?.hub.name || "Unknown Hub"}
                width={32}
                height={32}
                className="rounded-full"
                unoptimized
              />
              <CardTitle className="text-lg">{infraction?.hub.name}</CardTitle>
            </div>
            <div className="text-sm text-gray-400">
              Blacklisted {blacklistedOn}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-3">
            {isUserBlacklist ? (
              <>
                <Image
                  src={
                    infraction.user?.image ||
                    "https://api.dicebear.com/7.x/shapes/svg?seed=user"
                  }
                  alt={infraction.user?.name || "Unknown User"}
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                <div>
                  <div className="font-medium">
                    {infraction.user?.name || "Unknown User"}
                  </div>
                  <div className="text-xs text-gray-400">
                    User ID: {infraction.userId}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className="h-10 w-10 rounded-full bg-gray-800 flex items-center justify-center">
                  <Home className="h-5 w-5" />
                </div>
                <div>
                  <div className="font-medium">{infraction?.serverName}</div>
                  <div className="text-xs text-gray-400">
                    Server ID: {infraction?.serverId}
                  </div>
                </div>
              </>
            )}
          </div>

          <div className="p-3 rounded-md bg-[#0a0a0c] border border-gray-800">
            <div className="text-sm mb-2 text-gray-400">Original Reason:</div>
            <div className="text-sm">{infraction?.reason}</div>
          </div>
        </CardContent>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-6 pt-0">
            <div className="space-y-2">
              <Label htmlFor="reason">Reason for Removal</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for removing from blacklist"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[100px] bg-[#0a0a0c] border-gray-800"
                required
              />
              <p className="text-xs text-gray-400">
                Provide a reason for removing this{" "}
                {isUserBlacklist ? "user" : "server"} from the blacklist
              </p>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              disabled={isSubmitting}
              variant="destructive"
              className="w-full"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Removing from Blacklist...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Remove from Blacklist
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
