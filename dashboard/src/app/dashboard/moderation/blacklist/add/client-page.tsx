"use client";

import {
  DurationSelector,
  type DurationType,
} from "@/components/duration-selector";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import { addDays } from "date-fns";
import {
  ArrowLeft,
  ArrowRight,
  Check,
  Home,
  Loader2,
  Shield,
  User
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

interface Hub {
  id: string;
  name: string;
  iconUrl: string;
}

export default function AddToBlacklistPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hubs, setHubs] = useState<Hub[]>([]);
  const [selectedHubId, setSelectedHubId] = useState<string>("");
  const [hubSelectKey, setHubSelectKey] = useState<number>(0); // Used to force re-render of Select component
  const [targetType, setTargetType] = useState<"user" | "server">("user");
  const searchParams = useSearchParams();
  const [userId, setUserId] = useState<string>("");
  const [isValidatingUser, setIsValidatingUser] = useState(false);
  const [validatedUser, setValidatedUser] = useState<{
    id: string;
    name: string;
    image?: string;
  } | null>(null);
  const [userError, setUserError] = useState<string>("");
  const [serverId, setServerId] = useState<string>("");
  const [serverName, setServerName] = useState<string>("");
  const [isValidatingServer, setIsValidatingServer] = useState(false);
  const [validatedServer, setValidatedServer] = useState<{
    id: string;
    name: string;
    icon?: string;
  } | null>(null);
  const [serverError, setServerError] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [duration, setDuration] = useState<DurationType>("7d");

  const { toast } = useToast();
  const router = useRouter();

  // Fetch user's hubs and handle URL parameters
  useEffect(() => {
    // Check if targetType is specified in URL
    const typeParam =
      searchParams.get("targetType") || searchParams.get("type");
    if (typeParam === "user" || typeParam === "server") {
      setTargetType(typeParam);
    }

    // Check if serverId is specified in URL
    const serverIdParam = searchParams.get("serverId");
    if (serverIdParam) {
      setServerId(serverIdParam);
      // Set target type to server if a serverId is provided
      setTargetType("server");
      // We'll validate the server after the component mounts
    }

    // Check if userId is specified in URL
    const userIdParam = searchParams.get("userId");
    if (userIdParam && (typeParam === "user" || !typeParam)) {
      setUserId(userIdParam);
      // We'll validate the user after the component mounts
    }

    // Check if hubId is specified in URL
    const hubIdParam = searchParams.get("hubId");

    const fetchHubs = async () => {
      try {
        setIsLoading(true);
        const response = await fetch("/api/hubs?moderated=true");

        if (!response.ok) {
          console.error(
            "Failed to fetch hubs:",
            response.status,
            response.statusText,
          );
          throw new Error("Failed to fetch hubs");
        }

        const data = await response.json();
        setHubs(data.hubs);

        if (data.hubs.length > 0) {
          // If hubId is specified in URL and exists in the user's hubs, select it
          if (
            hubIdParam &&
            data.hubs.some((hub: Hub) => hub.id === hubIdParam)
          ) {
            setSelectedHubId(hubIdParam);
          } else {
            // Otherwise, select the first hub
            setSelectedHubId(data.hubs[0].id);
          }
          // Force re-render of the Select component
          setHubSelectKey((prev) => prev + 1);
        }

        setIsLoading(false);

        // Auto-validate server or user ID if provided in URL
        if (serverIdParam) {
          validateDiscordServer(serverIdParam);
        } else if (userIdParam && (typeParam === "user" || !typeParam)) {
          validateDiscordUser(userIdParam);
        }
      } catch (error) {
        console.error("Error fetching hubs:", error);
        toast({
          title: "Error",
          description: "Failed to load hubs. Please try again.",
          variant: "destructive",
        });
        router.push("/dashboard/moderation/blacklist");
      }
    };

    fetchHubs();
  }, [toast, router, searchParams]);

  // Function to validate Discord user ID
  const validateDiscordUser = async (id: string) => {
    try {
      setIsValidatingUser(true);
      setUserError("");

      // Try to fetch from our database first
      const dbResponse = await fetch(`/api/users/${id}`);

      if (dbResponse.ok) {
        const dbData = await dbResponse.json();

        // Also fetch from Discord to ensure we have latest data
        const discordResponse = await fetch(`/api/discord/users/${id}`);

        if (discordResponse.ok) {
          const discordData = await discordResponse.json();

          // If Discord avatar is different from DB, update it
          if (discordData.user.avatar !== dbData.user.image) {
            await fetch(`/api/users/${id}`, {
              method: "PATCH",
              headers: { "Content-Type": "application/json" },
              body: JSON.stringify({ image: discordData.user.avatar }),
            });
          }

          setValidatedUser({
            id: discordData.user.id,
            name: discordData.user.username,
            image: discordData.user.avatar,
          });
          return;
        }

        // If Discord fetch fails, use DB data
        setValidatedUser({
          id: dbData.user.id,
          name: dbData.user.name,
          image: dbData.user.image,
        });
        return;
      }

      // If not in DB, try Discord API
      const discordResponse = await fetch(`/api/discord/users/${id}`);

      if (!discordResponse.ok) {
        throw new Error("User not found on Discord");
      }

      const discordData = await discordResponse.json();

      setValidatedUser({
        id: discordData.user.id,
        name: discordData.user.username,
        image: discordData.user.avatar,
      });
    } catch (error) {
      console.error("Error validating user:", error);
      setUserError(
        error instanceof Error ? error.message : "Failed to validate user",
      );
      setValidatedUser(null);
    } finally {
      setIsValidatingUser(false);
    }
  };

  // Function to validate Discord server ID
  const validateDiscordServer = async (id: string) => {
    try {
      setIsValidatingServer(true);
      setServerError("");

      // Try Discord API first to get the most up-to-date data
      const discordResponse = await fetch(`/api/discord/servers/${id}`);

      if (discordResponse.ok) {
        const discordData = await discordResponse.json();

        setValidatedServer({
          id: discordData.guild.id,
          name: discordData.guild.name,
          icon: discordData.guild.icon, // This will already be the full URL from the API
        });
        setServerName(discordData.guild.name);
        return;
      }

      // Fallback to database
      const dbResponse = await fetch(`/api/servers/${id}`);

      if (!dbResponse.ok) {
        throw new Error("Server not found");
      }

      const dbData = await dbResponse.json();

      setValidatedServer({
        id: dbData.server.id,
        name: dbData.server.name,
        // Use the Discord CDN URL if icon exists, otherwise use DiceBear
        icon:
          dbData.server.icon ||
          `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(
            id,
          )}`,
      });
      setServerName(dbData.server.name);
    } catch (error) {
      console.error("Error validating server:", error);
      setServerError(
        error instanceof Error ? error.message : "Failed to validate server",
      );
      setValidatedServer(null);
    } finally {
      setIsValidatingServer(false);
    }
  };

  const getExpirationDate = (): string | null => {
    if (duration === "permanent") return null;

    const now = new Date();
    let expirationDate: Date;

    switch (duration) {
      case "1d":
        expirationDate = addDays(now, 1);
        break;
      case "7d":
        expirationDate = addDays(now, 7);
        break;
      case "30d":
        expirationDate = addDays(now, 30);
        break;
      case "90d":
        expirationDate = addDays(now, 90);
        break;
      case "365d":
        expirationDate = addDays(now, 365);
        break;
      default:
        expirationDate = addDays(now, 7);
    }

    return expirationDate.toISOString();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedHubId) {
      toast({
        title: "Error",
        description: "Please select a hub.",
        variant: "destructive",
      });
      return;
    }

    if (targetType === "user") {
      if (!userId) {
        toast({
          title: "Error",
          description: "Please enter a user ID.",
          variant: "destructive",
        });
        return;
      }

      if (!validatedUser) {
        toast({
          title: "Error",
          description: userError || "Please validate the user ID first.",
          variant: "destructive",
        });
        return;
      }
    }

    if (targetType === "server") {
      if (!serverId) {
        toast({
          title: "Error",
          description: "Please enter a server ID.",
          variant: "destructive",
        });
        return;
      }

      if (!validatedServer) {
        toast({
          title: "Error",
          description: serverError || "Please validate the server ID first.",
          variant: "destructive",
        });
        return;
      }

      // Server name is auto-populated from validation, no need to check
    }

    if (!reason) {
      toast({
        title: "Error",
        description: "Please enter a reason for the blacklist.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const expiresAt = getExpirationDate();

      const response = await fetch("/api/infractions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          hubId: selectedHubId,
          type: "BLACKLIST",
          reason,
          expiresAt,
          ...(targetType === "user" ? { userId } : { serverId, serverName }),
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to add to blacklist");
      }

      toast({
        title: "Success",
        description: `${
          targetType === "user" ? "User" : "Server"
        } has been added to the blacklist.`,
      });

      router.push("/dashboard/moderation/blacklist");
    } catch (error) {
      console.error("Error adding to blacklist:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to add to blacklist",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="sm"
            className="mr-2 border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
            asChild
          >
            <Link href="/dashboard/moderation/blacklist">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Link>
          </Button>
          <h1 className="text-2xl font-bold tracking-tight">
            Add to Blacklist
          </h1>
        </div>
        <p className="text-gray-400 text-sm max-w-md">
          Blacklisted users and servers will be prevented from joining or
          connecting to your hubs.
        </p>
      </div>
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <form onSubmit={handleSubmit}>
          <CardHeader className="border-b border-gray-800/50 pb-4">
            <CardTitle className="text-xl flex items-center">
              <Shield className="h-5 w-5 mr-2 text-red-400" />
              Blacklist Details
            </CardTitle>
            <CardDescription>
              Add a user or server to the blacklist
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {hubs.length === 0 && !isLoading && (
              <div className="p-4 mb-4 bg-amber-500/10 border border-amber-500/30 rounded-md">
                <h3 className="text-amber-400 font-medium mb-1">
                  No Hubs Available
                </h3>
                <p className="text-sm text-gray-300">
                  You need to be an owner or moderator of at least one hub to
                  add blacklist entries.
                  <Link
                    href="/dashboard/hubs"
                    className="text-primary hover:underline ml-1"
                  >
                    Go to your hubs
                  </Link>
                </p>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="hub">Hub</Label>
              <div className="space-y-2">
                <Select
                  key={hubSelectKey} // Force re-render when hubs are loaded
                  value={selectedHubId}
                  onValueChange={(value) => {
                    setSelectedHubId(value);
                  }}
                >
                  <SelectTrigger
                    id="hub"
                    className="bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30"
                  >
                    <SelectValue placeholder="Select a hub" />
                  </SelectTrigger>
                  <SelectContent className="bg-gradient-to-b from-gray-900/95 to-gray-950/95 backdrop-blur-md border border-gray-800/50">
                    {hubs.length > 0 ? (
                      hubs.map((hub) => (
                        <SelectItem key={hub.id} value={hub.id}>
                          {hub.name}
                        </SelectItem>
                      ))
                    ) : (
                      <div className="p-2 text-sm text-gray-400">
                        No hubs found. You need to be an owner or moderator of
                        at least one hub.
                      </div>
                    )}
                  </SelectContent>
                </Select>
              </div>
              <p className="text-xs text-gray-400">
                Select the hub to add the blacklist entry to
              </p>
            </div>

            <div className="space-y-2">
              <Label>Target Type</Label>
              <Tabs
                defaultValue="user"
                value={targetType}
                onValueChange={(value) =>
                  setTargetType(value as "user" | "server")
                }
                className="space-y-4"
              >
                <div className="relative overflow-x-auto no-scrollbar">
                  <TabsList className="bg-gray-900 border border-gray-800 rounded-xl p-1 w-full min-w-max flex gap-1 justify-start">
                    <TabsTrigger
                      value="user"
                      className="flex-1 min-w-[100px] data-[state=active]:bg-indigo-600 data-[state=active]:text-white data-[state=active]:shadow-md cursor-pointer whitespace-nowrap rounded-lg px-3 sm:px-4 py-2 transition-all duration-200 hover:bg-gray-800 text-xs sm:text-sm"
                    >
                      <User className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                      <span className="truncate">User</span>
                    </TabsTrigger>
                    <TabsTrigger
                      value="server"
                      className="flex-1 min-w-[100px] data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-md cursor-pointer whitespace-nowrap rounded-lg px-3 sm:px-4 py-2 transition-all duration-200 hover:bg-gray-800 text-xs sm:text-sm"
                    >
                      <Home className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 flex-shrink-0" />
                      <span className="truncate">Server</span>
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="user" className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="userId">User ID</Label>
                      <div className="flex gap-2">
                        <Input
                          id="userId"
                          placeholder="Enter Discord user ID"
                          value={userId}
                          onChange={(e) => {
                            setUserId(e.target.value);
                            // Clear validation when user changes the ID
                            if (
                              validatedUser &&
                              validatedUser.id !== e.target.value
                            ) {
                              setValidatedUser(null);
                              setUserError("");
                            }
                          }}
                          className={`bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30 focus-visible:ring-indigo-500/30 flex-1 ${
                            userError
                              ? "border-red-500 focus:ring-red-500/30 focus-visible:ring-red-500/30"
                              : validatedUser
                                ? "border-green-500 focus:ring-green-500/30 focus-visible:ring-green-500/30"
                                : ""
                          }`}
                        />
                        <Button
                          type="button"
                          variant={validatedUser ? "outline" : "default"}
                          onClick={() => validateDiscordUser(userId)}
                          disabled={isValidatingUser || !userId.trim()}
                          className={
                            validatedUser
                              ? "border-green-500 bg-green-500/10 text-green-400"
                              : "border-primary-alt/20 bg-primary/20 text-primary-alt hover:bg-primary/10 cursor-pointer"
                          }
                        >
                          {isValidatingUser ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Checking...
                            </>
                          ) : validatedUser ? (
                            <>
                              <Check className="h-4 w-4 mr-2" />
                              Verified
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-4 w-4 mr-2" />
                              Verify ID
                            </>
                          )}
                        </Button>
                      </div>
                      {userError ? (
                        <p className="text-xs text-red-500">{userError}</p>
                      ) : (
                        <p className="text-xs text-gray-400">
                          Enter the Discord ID of the user to blacklist
                        </p>
                      )}
                    </div>

                    {validatedUser && (
                      <div className="p-3 rounded-md bg-green-500/10 border border-green-500/30">
                        <div className="flex items-center gap-3">
                          {validatedUser.image ? (
                            <Image
                              src={validatedUser.image}
                              alt={validatedUser.name}
                              width={32}
                              height={32}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-800 flex items-center justify-center">
                              <User className="h-4 w-4 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium">
                              {validatedUser.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              ID: {validatedUser.id}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="server" className="space-y-4">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="serverId">Server ID</Label>
                      <div className="flex gap-2">
                        <Input
                          id="serverId"
                          placeholder="Enter Discord server ID"
                          value={serverId}
                          onChange={(e) => {
                            setServerId(e.target.value);
                            // Clear validation when server ID changes
                            if (
                              validatedServer &&
                              validatedServer.id !== e.target.value
                            ) {
                              setValidatedServer(null);
                              setServerError("");
                            }
                          }}
                          className={`bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30 focus-visible:ring-indigo-500/30 flex-1 ${
                            serverError
                              ? "border-red-500 focus:ring-red-500/30 focus-visible:ring-red-500/30"
                              : validatedServer
                                ? "border-green-500 focus:ring-green-500/30 focus-visible:ring-green-500/30"
                                : ""
                          }`}
                        />
                        <Button
                          type="button"
                          variant={validatedServer ? "outline" : "default"}
                          onClick={() => validateDiscordServer(serverId)}
                          disabled={isValidatingServer || !serverId.trim()}
                          className={
                            validatedServer
                              ? "border-green-500 bg-green-500/10 text-green-400"
                              : "border-primary-alt/20 bg-primary/20 text-primary-alt hover:bg-primary/10 cursor-pointer"
                          }
                        >
                          {isValidatingServer ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Checking...
                            </>
                          ) : validatedServer ? (
                            <>
                              <Check className="h-4 w-4 mr-2" />
                              Verified
                            </>
                          ) : (
                            <>
                              <ArrowRight className="h-4 w-4 mr-2" />
                              Verify ID
                            </>
                          )}
                        </Button>
                      </div>
                      {serverError ? (
                        <p className="text-xs text-red-500">{serverError}</p>
                      ) : (
                        <p className="text-xs text-gray-400">
                          Enter the Discord ID of the server to blacklist
                        </p>
                      )}
                    </div>

                    {validatedServer && (
                      <div className="p-3 rounded-md bg-green-500/10 border border-green-500/30">
                        <div className="flex items-center gap-3">
                          {validatedServer.icon ? (
                            <Image
                              src={validatedServer.icon}
                              alt={validatedServer.name}
                              width={32}
                              height={32}
                              className="rounded-full"
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-800 flex items-center justify-center">
                              <Home className="h-4 w-4 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium">
                              {validatedServer.name}
                            </div>
                            <div className="text-xs text-gray-400">
                              ID: {validatedServer.id}
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Server name is now hidden and auto-populated */}
                  <Input id="serverName" type="hidden" value={serverName} />
                </TabsContent>
              </Tabs>
            </div>

            <div className="space-y-2">
              <Label htmlFor="reason">Reason</Label>
              <Textarea
                id="reason"
                placeholder="Enter reason for blacklisting"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[100px] bg-gray-900/50 border-gray-800/50 focus:ring-indigo-500/30 focus-visible:ring-indigo-500/30"
              />
              <p className="text-xs text-gray-400">
                Provide a clear reason for the blacklist
              </p>
            </div>

            <DurationSelector
              value={duration}
              onChange={setDuration}
              description="Select how long the blacklist should last"
            />
          </CardContent>
          <CardFooter className="flex flex-col gap-3">
            {targetType === "user" &&
              !validatedUser &&
              userId.trim() &&
              !isValidatingUser && (
                <div className="w-full p-2 bg-amber-500/10 border border-amber-500/30 rounded-md text-center text-sm text-amber-400">
                  Please click &quot;Verify&quot; to validate the user ID before
                  submitting
                </div>
              )}
            {targetType === "server" &&
              !validatedServer &&
              serverId.trim() &&
              !isValidatingServer && (
                <div className="w-full p-2 bg-amber-500/10 border border-amber-500/30 rounded-md text-center text-sm text-amber-400">
                  Please click &quot;Verify&quot; to validate the server ID
                  before submitting
                </div>
              )}
            <Button
              type="submit"
              disabled={
                isSubmitting ||
                (targetType === "user" && !validatedUser) ||
                (targetType === "server" && !validatedServer) ||
                !reason.trim() ||
                !selectedHubId ||
                hubs.length === 0
              }
              className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 border-none"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Adding to Blacklist...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4 mr-2" />
                  Add to Blacklist
                </>
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
