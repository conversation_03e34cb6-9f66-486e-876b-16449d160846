import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission, getUserHubs } from "@/lib/permissions";
import { notFound, redirect } from "next/navigation";

export async function PermissionCheck({ hubId }: { hubId?: string }) {
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/moderation/blacklist/add`);
  }

  // If a specific hubId is provided, verify the user has permission for that hub
  if (hubId) {
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);

    // User must have at least moderator permissions to add to blacklist
    if (permissionLevel < PermissionLevel.MODERATOR) {
      notFound();
    }
  } else {
    // If no specific hubId, check if user has any hubs they can moderate
    const userHubs = await getUserHubs(session.user.id);
    const moderatedHubs = userHubs.filter(
      (hub) => hub.permissionLevel >= PermissionLevel.MODERATOR
    );

    if (moderatedHubs.length === 0) {
      redirect("/dashboard/hubs");
    }
  }

  // If we reach here, the user has permission
  return null;
}
