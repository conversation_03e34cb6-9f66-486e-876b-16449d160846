"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, RefreshCw } from "lucide-react";
import Link from "next/link";
import { signOut } from "next-auth/react";

export default function FixAccountPage() {
  const [isFixing, setIsFixing] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { toast } = useToast();
  const router = useRouter();

  const handleFixAccount = async () => {
    setIsFixing(true);
    setErrorMessage("");

    try {
      const response = await fetch("/api/auth/fix-accounts");

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to fix account");
      }

      toast({
        title: "Account Fixed",
        description: "Your account has been fixed successfully.",
      });

      setIsSuccess(true);
    } catch (error) {
      console.error("Error fixing account:", error);
      setErrorMessage(
        error instanceof Error ? error.message : "Failed to fix account",
      );

      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to fix account",
        variant: "destructive",
      });
    } finally {
      setIsFixing(false);
    }
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/login" });
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-[#0a0a0c]">
      <div className="mx-auto max-w-md space-y-6 p-6">
        <Card className="border-gray-800 bg-[#0f1117]">
          <CardHeader>
            <CardTitle>Fix Account Connection</CardTitle>
            <CardDescription>
              {isSuccess
                ? "Your account has been fixed successfully."
                : "Fix the connection between your Discord account and InterChat."}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isSuccess ? (
              <p className="text-green-400 mb-4">
                Your account has been fixed. You should now be able to access
                all dashboard features.
              </p>
            ) : (
              <p className="text-gray-400 mb-4">
                If you&apos;re seeing errors about missing Discord accounts or
                access tokens, this will attempt to fix the connection.
              </p>
            )}

            {errorMessage && (
              <p className="text-red-400 mb-4">Error: {errorMessage}</p>
            )}
          </CardContent>
          <CardFooter className="flex flex-col space-y-2">
            {isSuccess ? (
              <>
                <Button
                  className="w-full"
                  onClick={() => router.push("/dashboard")}
                >
                  Return to Dashboard
                </Button>
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={handleSignOut}
                >
                  Sign Out and Sign In Again
                </Button>
              </>
            ) : (
              <>
                <Button
                  className="w-full"
                  onClick={handleFixAccount}
                  disabled={isFixing}
                >
                  {isFixing ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Fixing...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Fix Account
                    </>
                  )}
                </Button>
                <Button variant="outline" className="w-full" asChild>
                  <Link href="/dashboard">Back to Dashboard</Link>
                </Button>
              </>
            )}
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
