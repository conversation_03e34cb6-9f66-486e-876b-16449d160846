import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import {
  Check,
  Clock,
  Shield,
  X,
} from "lucide-react";
import { AppealsHero } from "@/components/dashboard/appeals/appeals-hero";
import Image from "next/image";
import Link from "next/link";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import prisma from "@/lib/prisma";
import type { AppealStatus, Prisma } from "@/lib/generated/prisma/client";
import type { Metadata } from "next";
import { AppealsFiltersCard } from "./components/appeals-filters";
import { AppealsPaginationCard } from "./components/appeals-pagination";
import { AppealActionButtons } from "./components/appeal-action-buttons";

export const metadata: Metadata = {
  title: "Appeals | InterChat Dashboard",
  description: "Manage appeals for your hubs",
};

interface AppealUser {
  id: string;
  name: string | null;
  image: string | null;
}

interface AppealInfraction {
  id: string;
  hubId: string;
  type: string;
  status: string;
  reason: string;
  hub: {
    id: string;
    name: string;
    iconUrl: string;
  };
  user: AppealUser | null;
}

interface Appeal {
  id: string;
  infractionId: string;
  userId: string;
  reason: string;
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  createdAt: Date;
  updatedAt: Date;
  user: AppealUser;
  infraction: AppealInfraction;
}

interface Hub {
  id: string;
  name: string;
  iconUrl: string | null;
}

// Helper function to get hubs that the user has access to
async function getUserAccessibleHubs(userId: string): Promise<Hub[]> {
  // Get hubs where user is owner
  const ownedHubs = await prisma.hub.findMany({
    where: { ownerId: userId },
    select: {
      id: true,
      name: true,
      iconUrl: true,
    },
  });

  // Get hubs where user is a moderator or manager
  const moderatedHubs = await prisma.hubModerator.findMany({
    where: { userId },
    select: {
      hub: {
        select: {
          id: true,
          name: true,
          iconUrl: true,
        },
      },
    },
  });

  // Combine the hubs and remove duplicates
  const allHubs = [...ownedHubs, ...moderatedHubs.map((mod) => mod.hub)];

  // Remove duplicates by ID
  const uniqueHubs = Array.from(
    new Map(allHubs.map((hub) => [hub.id, hub])).values(),
  );

  // Sort by name
  uniqueHubs.sort((a, b) => a.name.localeCompare(b.name));

  return uniqueHubs;
}

// Server-side function to fetch appeals data
async function getAppealsData(searchParams: {
  status?: string;
  userId?: string;
  infractionId?: string;
  hubId?: string;
  page?: string;
}) {
  const session = await auth();
  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/appeals");
  }

  const status = searchParams.status as AppealStatus | undefined;
  const userId = searchParams.userId;
  const infractionId = searchParams.infractionId;
  const hubId = searchParams.hubId;
  const page = parseInt(searchParams.page || "1");
  const limit = 10;
  const skip = (page - 1) * limit;

  // Get accessible hubs for the user
  const accessibleHubs = await getUserAccessibleHubs(session.user.id);

  if (accessibleHubs.length === 0) {
    // User has no moderator access to any hubs
    return {
      appeals: [],
      total: 0,
      page,
      totalPages: 1,
      hubs: [],
    };
  }

  // Build the where clause
  const whereClause: Prisma.AppealWhereInput = {
    infraction: {
      hubId: { in: accessibleHubs.map((hub) => hub.id) },
    },
  };

  if (status) {
    whereClause.status = status;
  }

  if (userId) {
    whereClause.userId = userId;
  }

  if (hubId) {
    // Verify the user has access to this specific hub
    if (!accessibleHubs.some((hub) => hub.id === hubId)) {
      return {
        appeals: [],
        total: 0,
        page,
        totalPages: 1,
        hubs: accessibleHubs,
      };
    }
    whereClause.infraction = {
      hubId: hubId,
    };
  }

  if (infractionId) {
    whereClause.infractionId = infractionId;
  }

  // Count total matching appeals for pagination
  const total = await prisma.appeal.count({
    where: whereClause,
  });

  // Fetch appeals with related data
  const appeals = await prisma.appeal.findMany({
    where: whereClause,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          image: true,
        },
      },
      infraction: {
        include: {
          hub: {
            select: {
              id: true,
              name: true,
              iconUrl: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: "desc",
    },
    skip,
    take: limit,
  });

  return {
    appeals,
    total,
    page,
    totalPages: Math.ceil(total / limit) || 1,
    hubs: accessibleHubs,
  };
}

export default async function AppealsPage(props: {
  searchParams: Promise<{
    status?: string;
    userId?: string;
    infractionId?: string;
    hubId?: string;
    page?: string;
  }>;
}) {
  const searchParams = await props.searchParams;
  const { appeals, page, totalPages, hubs } = await getAppealsData(searchParams);

  // Count pending and resolved appeals
  const pendingAppeals = appeals.filter(
    (appeal) => appeal.status === "PENDING",
  ).length;
  const resolvedAppeals = appeals.filter(
    (appeal) => appeal.status !== "PENDING",
  ).length;



  return (
    <div className="space-y-6">
      {/* Animated Hero Section */}
      <AppealsHero
        totalAppeals={appeals.length}
        pendingAppeals={pendingAppeals}
        resolvedAppeals={resolvedAppeals}
      />

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <h1 className="text-2xl font-bold tracking-tight">Appeals</h1>
      </div>

      {/* Filters */}
      <AppealsFiltersCard
        searchParams={searchParams}
        hubs={hubs}
      />

      {/* Appeals List */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg">Appeals List</CardTitle>
          <CardDescription>
            {searchParams.userId
              ? `Showing appeals from user ID: ${searchParams.userId}`
              : searchParams.infractionId
                ? `Showing appeals for infraction ID: ${searchParams.infractionId}`
                : searchParams.hubId
                  ? `Showing appeals for hub: ${hubs.find((h) => h.id === searchParams.hubId)?.name || searchParams.hubId}`
                  : "Showing all appeals"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {appeals.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 mx-auto text-gray-500 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Appeals Found</h3>
              <p className="text-gray-400 mb-4">
                {searchParams.userId
                  ? "This user has not submitted any appeals."
                  : searchParams.infractionId
                    ? "There are no appeals for this infraction."
                    : "There are no appeals to display."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {appeals.map((appeal) => (
                <AppealCard
                  key={appeal.id}
                  appeal={appeal}
                />
              ))}
            </div>
          )}
        </CardContent>
        {appeals.length > 0 && totalPages > 1 && (
          <AppealsPaginationCard
            currentPage={page}
            totalPages={totalPages}
          />
        )}
      </Card>
    </div>
  );
}

interface AppealCardProps {
  appeal: Appeal;
}

function AppealCard({ appeal }: AppealCardProps) {

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "PENDING":
        return (
          <Badge
            variant="outline"
            className="bg-yellow-500/10 text-yellow-500 border-yellow-500/20"
          >
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case "ACCEPTED":
        return (
          <Badge
            variant="outline"
            className="bg-green-500/10 text-green-500 border-green-500/20"
          >
            <Check className="h-3 w-3 mr-1" />
            Accepted
          </Badge>
        );
      case "REJECTED":
        return (
          <Badge
            variant="outline"
            className="bg-red-500/10 text-red-500 border-red-500/20"
          >
            <X className="h-3 w-3 mr-1" />
            Rejected
          </Badge>
        );
      default:
        return null;
    }
  };



  return (
    <div className="flex flex-col p-4 rounded-md bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm border border-gray-800/50">
      <div className="flex flex-col sm:flex-row sm:items-start justify-between mb-3 gap-3">
        <div className="flex items-center gap-3">
          <div className="h-10 w-10 rounded-full border-2 border-blue-500/20 overflow-hidden flex-shrink-0">
            <Image
              src={appeal.user.image || "/images/default-avatar.png"}
              alt={appeal.user.name || "User"}
              width={40}
              height={40}
              className="object-cover"
              style={{ width: "100%", height: "100%" }}
            />
          </div>
          <div className="min-w-0">
            <div className="font-medium flex items-center flex-wrap">
              <span className="truncate mr-2">
                {appeal.user.name || "Unknown User"}
              </span>
              <span className="text-gray-500 mx-1 hidden sm:inline">•</span>
              <span className="text-xs text-gray-400 truncate">
                {appeal.userId}
              </span>
            </div>
            <div className="text-xs text-gray-400 flex items-center gap-1">
              <Clock className="h-3 w-3 text-gray-500" />
              Submitted{" "}
              {formatDistanceToNow(new Date(appeal.createdAt), {
                addSuffix: true,
              })}
            </div>
          </div>
        </div>
        <div className="self-start mt-2 sm:mt-0">
          {getStatusBadge(appeal.status)}
        </div>
      </div>
      <div className="mb-3">
        <h4 className="text-sm font-medium text-gray-300 mb-1">
          Appeal Reason:
        </h4>
        <p className="text-sm text-gray-300">{appeal.reason}</p>
      </div>
      <div className="bg-gray-900/30 p-3 rounded-md mb-3">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-gray-400" />
          <h4 className="text-sm font-medium">Related Infraction</h4>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">ID:</span>
          <Link
            href={`/dashboard/infractions/${appeal.infractionId}`}
            className="text-xs text-blue-400 hover:underline"
          >
            {appeal.infractionId}
          </Link>
        </div>
        <div className="flex items-center gap-2 mb-1">
          <span className="text-xs text-gray-400">Hub:</span>
          <span className="text-xs">{appeal.infraction.hub.name}</span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-xs text-gray-400">Reason:</span>
          <span className="text-xs truncate">{appeal.infraction.reason}</span>
        </div>
      </div>
      {appeal.status === "PENDING" && (
        <div className="flex flex-wrap gap-2 justify-end mt-2">
          <AppealActionButtons appealId={appeal.id} />
        </div>
      )}
    </div>
  );
}
