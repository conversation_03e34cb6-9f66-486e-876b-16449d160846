"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { Check, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface AppealActionButtonsProps {
  appealId: string;
}

export function AppealActionButtons({ appealId }: AppealActionButtonsProps) {
  const { toast } = useToast();
  const router = useRouter();
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusChange = async (newStatus: "ACCEPTED" | "REJECTED") => {
    setIsUpdating(true);
    try {
      const response = await fetch(`/api/appeals/${appealId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || `Failed to ${newStatus.toLowerCase()} appeal`,
        );
      }

      toast({
        title: `Appeal ${newStatus === "ACCEPTED" ? "Accepted" : "Rejected"}`,
        description:
          newStatus === "ACCEPTED"
            ? "The infraction has been appealed."
            : "The appeal has been rejected.",
      });

      // Refresh the page to show updated data
      router.refresh();
    } catch (error) {
      console.error(`Error ${newStatus.toLowerCase()}ing appeal:`, error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : `Failed to ${newStatus.toLowerCase()} appeal`,
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="border-red-500/30 text-red-500 hover:bg-red-900/30 hover:text-red-300 hover:border-red-700/50 flex-1 sm:flex-initial"
        onClick={() => handleStatusChange("REJECTED")}
        disabled={isUpdating}
      >
        <X className="h-4 w-4 mr-1" />
        Reject
      </Button>
      <Button
        variant="default"
        size="sm"
        className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-600/80 hover:to-green-700/80 border-none flex-1 sm:flex-initial"
        onClick={() => handleStatusChange("ACCEPTED")}
        disabled={isUpdating}
      >
        <Check className="h-4 w-4 mr-1" />
        Accept
      </Button>
    </>
  );
}
