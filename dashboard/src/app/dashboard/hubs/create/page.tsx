import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { ArrowLeft, Sparkles, Zap, Shield, Globe } from "lucide-react";
import Link from "next/link";
import { auth } from "@/auth";
import { redirect } from "next/navigation";
import type { Metadata } from "next";
import { HubCreateForm } from "./components/hub-create-form";


export const metadata: Metadata = {
  title: "Create Hub | InterChat Dashboard",
  description: "Create a new InterChat hub to connect Discord communities",
};

export default async function CreateHubPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/hubs/create");
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-indigo-900/20 via-gray-900/5 to-transparent" />
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:50px_50px]" />

      {/* Content */}
      <div className="relative">
        {/* Header */}
        <div className="border-b border-gray-800/50 bg-gray-950/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                  asChild
                >
                  <Link href="/dashboard/hubs">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Hubs
                  </Link>
                </Button>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
                    Create New Hub
                  </h1>
                  <p className="text-gray-400 text-sm">
                    Build a community that connects Discord servers worldwide
                  </p>
                </div>
              </div>

              {/* Feature Highlights */}
              <div className="hidden lg:flex items-center gap-6 text-sm text-gray-400">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-yellow-400" />
                  <span>Instant Setup</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-400" />
                  <span>Full Control</span>
                </div>
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-blue-400" />
                  <span>Global Reach</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="container mx-auto px-4 py-12">
          <div className="max-w-4xl mx-auto">
            {/* Hero Section */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-indigo-500/10 border border-indigo-500/20 text-indigo-300 text-sm mb-6">
                <Sparkles className="h-4 w-4" />
                <span>Create Your Community Hub</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">
                Connect Discord servers across the globe
              </h2>
              <p className="text-gray-400 text-lg max-w-2xl mx-auto">
                Create a hub to enable cross-server communication, build larger communities,
                and connect with Discord servers that share your interests.
              </p>
            </div>

            {/* Form */}
            <HubCreateForm />
          </div>
        </div>
      </div>
    </div>
  );
}
