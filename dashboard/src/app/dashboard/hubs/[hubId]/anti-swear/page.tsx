import { auth } from "@/auth";
import { AntiSwearForm } from "@/components/dashboard/hubs/anti-swear-form";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HubLayout } from "@/components/dashboard/hubs/hub-layout";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import prisma from "@/lib/prisma";
import { ArrowLeft, AlertCircle, Shield } from "lucide-react";
import Link from "next/link";
import { notFound, redirect } from "next/navigation";

interface HubAntiSwearPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export default async function HubAntiSwearPage({
  params,
}: HubAntiSwearPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/anti-swear`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const canModerate = permissionLevel >= PermissionLevel.MODERATOR;

  // Only allow moderators and managers to access this page
  if (permissionLevel < PermissionLevel.MODERATOR) {
    redirect(`/dashboard/hubs/${hubId}`);
  }

  const hub = await prisma.hub.findUnique({
    where: { id: hubId },
    select: {
      id: true,
      name: true,
      description: true,
      iconUrl: true,
      private: true,
      nsfw: true,
      blockWords: {
        select: {
          id: true,
          name: true,
        },
      },
      _count: {
        select: {
          connections: {
            where: { connected: true }
          }
        }
      }
    },
  });

  if (!hub) {
    notFound();
  }

  // Prepare hub data for the layout
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    iconUrl: hub.iconUrl,
    private: hub.private,
    nsfw: hub.nsfw,
    connectionCount: hub._count.connections,
  };

  return (
    <HubLayout
      hub={hubData}
      currentTab="anti-swear"
      canModerate={canModerate}
      canEdit={canEdit}
    >

      <Card className="border border-purple-800/30 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm mb-6">
        <CardHeader>
          <CardTitle className="flex items-center text-gradient bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
            <Shield className="h-5 w-5 mr-2 text-purple-400" />
            Anti-Swear Configuration
          </CardTitle>
          <CardDescription>
            Configure word filters to block inappropriate content in your hub
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-gradient-to-br from-gray-950/80 to-purple-950/10 p-5 rounded-md mb-6 border border-amber-800/30">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 mr-3 text-amber-500 mt-0.5" />
              <div className="space-y-3">
                <p className="text-amber-200 font-medium">Pattern Matching Guide</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="bg-gray-950/70 p-3 rounded-md border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200">
                    <p className="font-medium text-purple-300 mb-1">Exact Match</p>
                    <code className="bg-gray-800/70 px-2 py-1 rounded text-white">word</code>
                    <p className="text-sm text-gray-300 mt-2">Only matches exactly &quot;word&quot;</p>
                  </div>
                  <div className="bg-gray-950/70 p-3 rounded-md border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200">
                    <p className="font-medium text-purple-300 mb-1">Prefix Match</p>
                    <code className="bg-gray-800/70 px-2 py-1 rounded text-white">word*</code>
                    <p className="text-sm text-gray-300 mt-2">Matches &quot;word&quot;, &quot;words&quot;, &quot;wordy&quot;</p>
                  </div>
                  <div className="bg-gray-950/70 p-3 rounded-md border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200">
                    <p className="font-medium text-purple-300 mb-1">Suffix Match</p>
                    <code className="bg-gray-800/70 px-2 py-1 rounded text-white">*word</code>
                    <p className="text-sm text-gray-300 mt-2">Matches &quot;word&quot;, &quot;badword&quot;, &quot;myword&quot;</p>
                  </div>
                  <div className="bg-gray-950/70 p-3 rounded-md border border-gray-800/50 hover:border-gray-700/50 transition-all duration-200">
                    <p className="font-medium text-purple-300 mb-1">Contains Match</p>
                    <code className="bg-gray-800/70 px-2 py-1 rounded text-white">*word*</code>
                    <p className="text-sm text-gray-300 mt-2">Matches any text containing &quot;word&quot;</p>
                  </div>
                </div>
                <p className="text-sm text-gray-300 bg-gray-950/70 p-3 rounded-md border border-gray-800/50">
                  Separate words or phrases with a comma (dog, cat, tiger) or new line.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <AntiSwearForm hubId={hubId} canEdit={canEdit} />
    </HubLayout>
  );
}
