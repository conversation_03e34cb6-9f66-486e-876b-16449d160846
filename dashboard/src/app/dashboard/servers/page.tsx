import { getServers } from "@/actions/server-actions";
import { auth } from "@/auth";
import { FixAccountButton } from "@/components/dashboard/fix-account-button";
import { AnimatedServersHero } from "@/components/dashboard/servers/animated-servers-hero";
import { ConnectionsGrid } from "@/components/dashboard/servers/connections-grid";
import { ConnectionsSkeleton } from "@/components/dashboard/servers/connections-skeleton";
import { ServerGrid } from "@/components/dashboard/servers/server-grid";
import { ServersPageSkeleton } from "@/components/dashboard/servers/servers-page-skeleton";
import { ServersSkeleton } from "@/components/dashboard/servers/servers-skeleton";
import { UnderlinedTabs } from "@/components/dashboard/underlined-tabs";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { TabsContent } from "@/components/ui/tabs";
import prisma from "@/lib/prisma";
import { PlusCircle } from "lucide-react";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: "Servers | InterChat Dashboard",
  description: "Manage your Discord servers connected to InterChat",
};

export default function ServersPage(props: {
  searchParams: Promise<{ hubId?: string }>;
}) {
  return (
    <Suspense fallback={<ServersPageSkeleton />}>
      <ErrorBoundary>
        <ServersContent searchParams={props.searchParams} />
      </ErrorBoundary>
    </Suspense>
  );
}

// Simple error boundary component
function ErrorBoundary({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}

async function ServersContent({
  searchParams,
}: {
  searchParams: Promise<{ hubId?: string }>;
}) {
  const session = await auth();
  const hubId = (await searchParams)?.hubId;

  if (!session?.user) {
    redirect("/login?callbackUrl=/dashboard/servers");
  }

  // If hubId is provided, fetch hub details
  let selectedHub = null;
  if (hubId) {
    try {
      selectedHub = await prisma.hub.findUnique({
        where: { id: hubId },
        select: {
          id: true,
          name: true,
          description: true,
          iconUrl: true,
          private: true,
        },
        
      });

      if (!selectedHub) {
        console.error(`Hub with ID ${hubId} not found`);
      }
    } catch (error) {
      console.error(`Error fetching hub details: ${error}`);
    }
  }

  // Fetch servers using the server action with a timeout
  const serversPromise = getServers();

  // Create a timeout promise
  const timeoutPromise = new Promise<{ error: string; status: number }>(
    (resolve) => {
      setTimeout(() => {
        resolve({
          error: "Request timed out. Please refresh the page to try again.",
          status: 408,
        });
      }, 10000); // 10 second timeout
    }
  );

  // Race between the servers promise and the timeout
  const serversResult = await Promise.race([serversPromise, timeoutPromise]);

  if ("error" in serversResult) {
    // Handle error - for now, we'll just show an empty list
    console.error(`Error fetching servers: ${serversResult.status}`);
    console.error(serversResult.error);
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <div className="flex items-center gap-2">
              <h1 className="text-2xl font-bold">Servers</h1>
              <FixAccountButton />
            </div>
            <p className="text-gray-400">
              Manage your Discord servers connected to InterChat
            </p>
          </div>
          <Button asChild>
            <Link
              href="https://discord.com/oauth2/authorize?client_id=769921109209907241"
              target="_blank"
              rel="noopener noreferrer"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Add Bot to Server
            </Link>
          </Button>
        </div>
        <Card className="border-gray-800 bg-[#0f1117]">
          <CardHeader>
            <CardTitle>Error Loading Servers</CardTitle>
            <CardDescription>
              There was an error loading your servers. Please try again later.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-red-400 mb-4">{serversResult.error}</p>
            {serversResult.error.includes("/dashboard/fix-account") && (
              <Button asChild>
                <Link href="/dashboard/fix-account">Fix Account Issues</Link>
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    );
  }

  const servers = serversResult.data;
  const connections = servers.flatMap((server) => server.connections);

  // Separate servers into those with and without the bot
  const serversWithBot = servers.filter((server) => server.botAdded);
  const serversWithoutBot = servers.filter((server) => !server.botAdded);

  // Count connected servers (servers with the bot)
  const connectedServersCount = serversWithBot.length;

  // Count total connections
  const totalConnectionsCount = servers.reduce(
    (acc, server) => acc + (server.connections?.length || 0),
    0
  );

  return (
    <div className="space-y-6">
      {/* Animated Hero Section */}
      <AnimatedServersHero
        totalServers={servers.length}
        connectedServers={connectedServersCount}
        totalConnections={totalConnectionsCount}
      />
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">My Servers</h1>
        <Button
          asChild
          className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
        >
          <Link
            href="https://discord.com/oauth2/authorize?client_id=769921109209907241"
            target="_blank"
            rel="noopener noreferrer"
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Bot to Server
          </Link>
        </Button>
      </div>
      {selectedHub && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6 pb-6">
            <div className="flex items-center gap-4">
              <Image
                src={selectedHub.iconUrl}
                alt={selectedHub.name}
                width={56}
                height={56}
                className="rounded-full"
              />

              <div className="flex-1">
                <h3 className="text-lg font-semibold">
                  Connect to {selectedHub.name}
                </h3>
                <p className="text-sm text-gray-400">
                  Select a server below to connect to this hub
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      <UnderlinedTabs
        defaultValue="all"
        className="w-full space-y-6"
        tabs={[
          {
            value: "all",
            label: "All Servers",
            color: "indigo",
          },
          {
            value: "connected",
            label: "Connected",
            color: "blue",
          },
          {
            value: "not-connected",
            label: "Not Connected",
            color: "purple",
          },
          {
            value: "connections",
            label: "Connections",
            color: "green",
          },
        ]}
      >
        <TabsContent value="all" className="space-y-6">
          <Suspense fallback={<ServersSkeleton />}>
            <ServerGrid
              servers={servers}
              showConnectButton={!!selectedHub}
              selectedHubId={selectedHub?.id}
            />
          </Suspense>
        </TabsContent>

        <TabsContent value="connected" className="space-y-6">
          <Suspense fallback={<ServersSkeleton />}>
            {serversWithBot.length > 0 ? (
              <ServerGrid
                servers={serversWithBot}
                showConnectButton={!!selectedHub}
                selectedHubId={selectedHub?.id}
              />
            ) : (
              <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>No Connected Servers</CardTitle>
                  <CardDescription>
                    You don&apos;t have any servers with InterChat added.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-400 mb-4">
                    {selectedHub
                      ? `Add InterChat to your Discord servers to connect them to ${selectedHub.name}.`
                      : "Add InterChat to your Discord servers to start connecting them to hubs."}
                  </p>
                  <Button
                    asChild
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none"
                  >
                    <Link
                      href="https://discord.com/oauth2/authorize?client_id=769921109209907241"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Bot to Server
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </Suspense>
        </TabsContent>

        <TabsContent value="not-connected" className="space-y-6">
          <Suspense fallback={<ServersSkeleton />}>
            {serversWithoutBot.length > 0 ? (
              <ServerGrid
                servers={serversWithoutBot}
                showConnectButton={!!selectedHub}
                selectedHubId={selectedHub?.id}
              />
            ) : (
              <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>All Servers Connected</CardTitle>
                  <CardDescription>
                    All your servers already have InterChat added.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-400">
                    {selectedHub
                      ? "You've added InterChat to all your servers. Select one above to connect it to this hub."
                      : "Great job! You've added InterChat to all your servers."}
                  </p>
                </CardContent>
              </Card>
            )}
          </Suspense>
        </TabsContent>

        <TabsContent value="connections" className="space-y-6">
          <Suspense fallback={<ConnectionsSkeleton />}>
            <ConnectionsGrid connections={connections} />
          </Suspense>
        </TabsContent>
      </UnderlinedTabs>
    </div>
  );
}
