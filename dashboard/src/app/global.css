@import "tailwindcss";
@import "fumadocs-ui/css/neutral.css";
@import "fumadocs-ui/css/preset.css";

@source '../../node_modules/fumadocs-ui/dist/**/*.js';

@layer base {
  :root {
    --radius: 0.5rem;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  /* Mobile-friendly tab scrolling */
  .mobile-tab-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* Ensure tabs don't break on mobile */
  .mobile-tab-container {
    min-width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
  }

  /* Better touch targets on mobile */
  @media (max-width: 640px) {
    .mobile-tab-trigger {
      min-height: 44px; /* iOS recommended touch target */
      min-width: 44px;
    }
  }
}

:root {
  --color-fd-primary: #9172d8;
}

@theme {
  --color-background: hsl(228 9% 11%);
  --color-primary: var(--color-fd-primary);
  --color-primary-alt: #5e7de5;
  --color-border: hsl(228 9% 11%);
  --color-input: hsl(228 9% 11%);
  --color-ring: hsl(235 85.6% 64.7%);
  --color-foreground: hsl(210 40% 98%);

  --color-card: hsl(228 9% 11%);
  --color-card-foreground: hsl(210 40% 98%);

  --color-popover: hsl(228 9% 11%);
  --color-popover-foreground: hsl(210 40% 98%);

  --color-primary-foreground: hsl(210 40% 98%);

  --color-secondary: hsl(228 9% 11%);
  --color-secondary-foreground: hsl(210 40% 98%);

  --color-muted: hsl(228 9% 11%);
  --color-muted-foreground: hsl(215 20.2% 65.1%);

  --color-accent: hsl(228 9% 11%);
  --color-accent-foreground: hsl(210 40% 98%);

  --color-destructive: hsl(0 62.8% 30.6%);
  --color-destructive-foreground: hsl(210 40% 98%);

  --color-border: hsl(228 9% 11%);
  --color-input: hsl(228 9% 11%);
  --color-ring: hsl(235 85.6% 64.7%);

  --radius-lg: var(--radius);
  --radius-md: calc(var(--radius) - 2px);
  --radius-sm: calc(var(--radius) - 4px);

  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-shiny-text: shiny-text 8s infinite;
  --animate-gradient: gradient 8s linear infinite;
  --animate-pulse-slow: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;

  @keyframes gradient {

    0%,
    100% {
      background-size: 200% 200%;
      background-position: left center;
    }

    50% {
      background-size: 200% 200%;
      background-position: right center;
    }
  }

  @keyframes pulse {

    0%,
    100% {
      opacity: 0.7;
    }

    50% {
      opacity: 0.4;
    }
  }
}

/* Animation utility classes */
.animate-gradient-slow {
  animation: var(--animate-gradient);
}

.animate-pulse-slow {
  animation: var(--animate-pulse-slow);
}

/* Mesh gradient background */
.bg-mesh-gradient {
  background-image: radial-gradient(at 40% 20%, hsla(28, 100%, 74%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 0%, hsla(189, 100%, 56%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 50%, hsla(355, 100%, 93%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 50%, hsla(340, 100%, 76%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 100%, hsla(22, 100%, 77%, 1) 0px, transparent 50%),
    radial-gradient(at 80% 100%, hsla(242, 100%, 70%, 1) 0px, transparent 50%),
    radial-gradient(at 0% 0%, hsla(343, 100%, 76%, 1) 0px, transparent 50%);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.7s forwards;
}

.animate-fade-in-delayed {
  animation: fadeIn 0.7s 0.2s forwards;
}

.animate-fade-in-more-delayed {
  animation: fadeIn 0.7s 0.4s forwards;
}

.animate-fade-in-staggered {
  animation: fadeIn 0.7s forwards;
}

/* Ensure dropdowns and popovers appear on top of other elements */
.dropdown-menu-content,
[data-radix-popper-content-wrapper],
[cmdk-root],
[cmdk-list],
[cmdk-item],
[role="dialog"],
[role="menu"],
[role="listbox"],
[role="combobox"] {
  z-index: 9999 !important;
}
