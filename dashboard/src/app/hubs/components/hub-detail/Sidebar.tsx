"use client";

import { useRouter } from "next/navigation";
import HubStatsCard from "./HubStatsCard";
import HubTagsCard from "./HubTagsCard";
import JoinHubCard from "./JoinHubCard";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export default function Sidebar({ hub }: { hub: SimplifiedHub }) {
  const router = useRouter();

  const handleTagClick = (tag: string) => {
    router.push(`/hubs?tags=${tag}`);
  };

  return (
    <div className="space-y-6">
      <JoinHubCard hub={hub} />
      <HubStatsCard hub={hub} />
      <HubTagsCard tags={hub.tags.map(t => t.name)} handleTagClick={handleTagClick} />
      {/* TODO: Either redo or delete this */}
      {/* <ModeratorsCard hub={hub} /> */}
    </div>
  );
}
