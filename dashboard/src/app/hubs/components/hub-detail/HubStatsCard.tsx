"use client";

import { Card, CardContent } from "@/components/ui/card";
import { formatDistanceToNow } from "date-fns";
import { Calendar, Clock, Heart, Home } from "lucide-react";
import { SimplifiedHub } from "@/hooks/use-infinite-hubs";

export default function HubStatsCard({ hub }: { hub: SimplifiedHub }) {
  const { connections, createdAt, upvotes } = hub;
  const connectionCount = connections.length;
  const upvoteCount = upvotes.length;

  const lastActive =
    connections.length > 0
      ? connections.reduce(
          (latest, connection) =>
            connection.lastActive && (!latest || connection.lastActive > latest)
              ? connection.lastActive
              : latest,
          null as Date | null,
        )
      : null;

  const lastActiveText = lastActive
    ? formatDistanceToNow(new Date(lastActive), { addSuffix: true })
    : "No recent activity";

  const createdDate = new Date(createdAt).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return (
    <Card className="bg-[#0f1117] border-gray-800">
      <div className="border-b border-gray-800 p-4">
        <h2 className="text-lg font-semibold">Hub Stats</h2>
      </div>
      <CardContent className="p-0">
        <div className="divide-y divide-gray-800">
          <div className="p-4 flex items-center justify-between hover:bg-gray-900/50 transition-colors">
            <div className="flex items-center gap-3">
              <Home className="h-5 w-5 text-purple-400" />
              <span>Connected Servers</span>
            </div>
            <span className="font-medium">{connectionCount}</span>
          </div>

          <div className="p-4 flex items-center justify-between hover:bg-gray-900/50 transition-colors">
            <div className="flex items-center gap-3">
              <Heart className="h-5 w-5 text-purple-400" />
              <span>Upvotes</span>
            </div>
            <span className="font-medium">{upvoteCount}</span>
          </div>

          <div className="p-4 flex items-center justify-between hover:bg-gray-900/50 transition-colors">
            <div className="flex items-center gap-3">
              <Clock className="h-5 w-5 text-purple-400" />
              <span>Last Active</span>
            </div>
            <span className="font-medium">{lastActiveText}</span>
          </div>

          <div className="p-4 flex items-center justify-between hover:bg-gray-900/50 transition-colors">
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-purple-400" />
              <span>Created On</span>
            </div>
            <span className="font-medium">{createdDate}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
