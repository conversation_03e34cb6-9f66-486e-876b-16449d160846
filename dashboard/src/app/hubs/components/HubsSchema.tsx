"use client";

import <PERSON><PERSON><PERSON> from "next/script";
import { useEffect, useState } from "react";
import type { SimplifiedHub } from "@/hooks/use-infinite-hubs";

interface HubListingSchemaProps {
  hubs: SimplifiedHub[];
  baseUrl: string;
}

export function HubListingSchema({ hubs, baseUrl }: HubListingSchemaProps) {
  const [schema, setSchema] = useState("");

  useEffect(() => {
    const itemListElement = hubs.map((hub, index) => ({
      "@type": "ListItem",
      position: index + 1,
      item: {
        "@type": "Organization",
        "@id": `${baseUrl}/hubs/${hub.id}`,
        name: hub.name,
        description: hub.description,
        url: `${baseUrl}/hubs/${hub.id}`,
        image: hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
        ...(hub.tags &&
          hub.tags.length > 0 && { keywords: hub.tags.join(", ") }),
        memberOf: {
          "@type": "Organization",
          name: "InterChat Discord Communities",
          url: `${baseUrl}/hubs`,
        },
        ...(hub.connections && {
          memberCount: hub.connections.length,
        }),
      },
    }));

    const schemaData = {
      "@context": "https://schema.org",
      "@type": "ItemList",
      itemListElement,
      numberOfItems: hubs.length,
      name: "InterChat Discord Community Hubs",
      description:
        "Browse and join active InterChat Discord cross-server hub. Connect your server to these active hubs and grow your community.",
    };

    setSchema(JSON.stringify(schemaData));
  }, [hubs, baseUrl]);

  return (
    <Script id="hub-listing-schema" type="application/ld+json">
      {schema}
    </Script>
  );
}

interface HubDetailSchemaProps {
  hub: SimplifiedHub;
  baseUrl: string;
}

export function HubDetailSchema({ hub, baseUrl }: HubDetailSchemaProps) {
  const [schema, setSchema] = useState("");

  useEffect(() => {
    const schemaData = {
      "@context": "https://schema.org",
      "@type": "Organization",
      "@id": `${baseUrl}/hubs/${hub.id}`,
      name: hub.name,
      description: hub.description,
      url: `${baseUrl}/hubs/${hub.id}`,
      logo: hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
      image: hub.bannerUrl || hub.iconUrl || `${baseUrl}/InterChatLogo.webp`,
      ...(hub.tags && hub.tags.length > 0 && { keywords: hub.tags.join(", ") }),
      memberOf: {
        "@type": "Organization",
        name: "InterChat Discord Communities",
        url: `${baseUrl}/hubs`,
      },
      ...(hub.connections && {
        memberCount: hub.connections.length,
      }),
      ...(hub.upvotes && {
        aggregateRating: {
          "@type": "AggregateRating",
          ratingValue: "5",
          ratingCount: hub.upvotes.length,
          bestRating: "5",
          worstRating: "1",
        },
      }),
    };

    setSchema(JSON.stringify(schemaData));
  }, [hub, baseUrl]);

  return (
    <Script id="hub-detail-schema" type="application/ld+json">
      {schema}
    </Script>
  );
}

interface BreadcrumbSchemaProps {
  items: Array<{
    name: string;
    item: string;
  }>;
}

export function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {
  const [schema, setSchema] = useState("");

  useEffect(() => {
    const itemListElement = items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.item,
    }));

    const schemaData = {
      "@context": "https://schema.org",
      "@type": "BreadcrumbList",
      itemListElement,
    };

    setSchema(JSON.stringify(schemaData));
  }, [items]);

  return (
    <Script id="breadcrumb-schema" type="application/ld+json">
      {schema}
    </Script>
  );
}
