'use client';

import { Footer } from '@/components/Footer';
import { EnhancedHubCard } from '@/components/hubs/EnhancedHubCard';
import { FeaturedHubsSection } from '@/components/hubs/FeaturedHubsSection';
import { HeroSection } from '@/components/hubs/HeroSection';
import { LeaderboardSection } from '@/components/hubs/LeaderboardSection';
import { SearchBar } from '@/components/hubs/SearchBar';
import { StatsBar } from '@/components/hubs/StatsBar';
import { Button } from '@/components/ui/button';
import { useInfiniteHubs } from '@/hooks/use-infinite-hubs';
import { Search } from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import React, { useState } from 'react';
import type { LeaderboardResponse, FeaturedHubsResponse } from '@/lib/platform-stats';

interface StatsData {
  activeServers: number;
  publicHubs: number;
  weeklyMessages: number;
}

interface RedesignedHubsPageProps {
  initialStats?: StatsData;
  initialLeaderboard?: LeaderboardResponse;
  initialFeaturedHubs?: FeaturedHubsResponse;
}

/**
 * Redesigned Hubs Page with new layout structure
 * Implements the complete design specification with all sections
 */
export function RedesignedHubsPage({ initialStats, initialLeaderboard, initialFeaturedHubs }: RedesignedHubsPageProps) {
  const [isFiltersSticky, setIsFiltersSticky] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Use initial stats from SSR instead of client-side fetch
  const stats = initialStats;

  // Fetch hubs based on current filters
  const {
    hubs,
    hasMore,
    isLoading: hubsLoading,
  } = useInfiniteHubs({
    search: searchTerm,
    limit: 12,
  });

  // Handle scroll to make filters sticky
  React.useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const heroHeight = window.innerHeight * 0.8; // Approximate hero height
      setIsFiltersSticky(scrollY > heroHeight);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="min-h-screen bg-gray-950">
      {/* 1️⃣ Hero Section (Top) */}
      <HeroSection />

      {/* 2️⃣ Stats Bar (below hero) */}
      <StatsBar stats={stats} />

      {/* Mobile Sticky Search Bar */}
      {isFiltersSticky && (
        <div className="sticky top-0 z-50 bg-gray-900/95 backdrop-blur-sm border-b border-gray-800/50 lg:hidden">
          <div className="container mx-auto px-4 py-3">
            <div className="relative">
              <SearchBar
                initialValue={searchTerm}
                placeholder="Search hubs..."
                size="default"
                variant="header"
                showButton={false}
                autoFocus={false}
                onSearch={setSearchTerm}
              />
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            </div>
          </div>
        </div>
      )}

      {/* 3️⃣ Featured Hubs / Staff Picks */}
      <FeaturedHubsSection initialData={initialFeaturedHubs} />

      {/* 4️⃣ Hub Listings Grid (Main content) */}
      <div className="bg-gray-950 py-12">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold text-white mb-2">All Hubs</h2>
          </motion.div>

          {/* Hub Cards Grid */}
          {hubsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-96 bg-gray-800/30 rounded-lg animate-pulse" />
              ))}
            </div>
          ) : hubs.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {hubs.map((hub, index) => (
                  <EnhancedHubCard key={hub.id} hub={hub} index={index} />
                ))}
              </div>

              {hasMore && (
                <div className="text-center space-y-4">
                  <Link href="/hubs/search">
                    <Button
                      variant="outline"
                      size="lg"
                      className="border-gray-700 bg-gray-800/50 hover:bg-gray-700/50 cursor-pointer"
                    >
                      Load More Hubs
                    </Button>
                  </Link>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-white mb-2">No hubs found</h3>
              <p className="text-gray-400 mb-6">
                Try adjusting your filters or search terms to find more communities.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* 5️⃣ Leaderboard Section (Competition) */}
      <LeaderboardSection initialData={initialLeaderboard} />

      {/* 7️⃣ Footer */}
      <Footer />
    </div>
  );
}
