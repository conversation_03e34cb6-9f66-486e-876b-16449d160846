import type { Metadata } from "next";
import { Suspense } from "react";
import { RedesignedHubsPage } from "./components/RedesignedHubsPage";
import { ClientOnboardingWrapper } from "./components/ClientOnboardingWrapper";
import { getPlatformStats, getStatsBarData, getLeaderboardData, getFeaturedHubsData } from "@/lib/platform-stats";

// Force dynamic rendering to ensure fresh data
export const dynamic = "force-dynamic";

/**
 * Generate metadata for SEO optimization
 */
export async function generateMetadata(): Promise<Metadata> {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || "https://interchat.tech";

  return {
    title:
      "Discover Active Cross-Server Communities | InterChat",
    description:
      "Find, join, and grow your favorite hubs across Discord servers. Discover active communities for gaming, art, technology, and more.",
    keywords: [
      "discord hubs",
      "discord server connections",
      "connect discord servers",
      "discord communities",
      "active discord servers",
      "discord server discovery",
      "join discord communities",
      "interchat hubs",
    ],
    openGraph: {
      title:
        "Discover InterChat Hubs | Connect Your Discord Server to Active Communities",
      description:
        "Browse and join thriving Discord communities with InterChat Hubs. Find active servers for gaming, art, technology, and more. Connect your Discord server today!",
      type: "website",
      url: `${baseUrl}/hubs`,
      images: [
        {
          url: `${baseUrl}/features/HubDiscovery.png`,
          width: 1200,
          height: 630,
          alt: "InterChat Hubs Discovery Page",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      images: [`${baseUrl}/features/HubDiscovery.png`],
      title: "Discover InterChat Hubs | Connect Your Discord Server",
      description:
        "Browse and join thriving Discord communities with InterChat Hubs. Find active servers for gaming, art, technology, and more.",
      creator: "@737_dev",
      site: "@interchatapp",
    },
    alternates: {
      canonical: `${baseUrl}/hubs`,
    },
  };
}

/**
 * Main page component for the redesigned Hubs discovery page
 * Features the new layout structure with hero section, stats bar, enhanced filters,
 * hub listings grid, leaderboard, featured hubs, and footer
 */
export default async function HubsPage() {
  // Fetch all data server-side for SSR
  const [platformStatsResponse, leaderboardResponse, featuredHubsResponse] = await Promise.all([
    getPlatformStats(),
    getLeaderboardData(),
    getFeaturedHubsData(),
  ]);

  const statsBarData = getStatsBarData(platformStatsResponse.data);

  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-950 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-400">Loading hub discovery...</p>
        </div>
      </div>
    }>
      {/* Redesigned Hub Discovery Page with new layout structure */}
      <RedesignedHubsPage
        initialStats={statsBarData}
        initialLeaderboard={leaderboardResponse}
        initialFeaturedHubs={featuredHubsResponse}
      />

      {/* Onboarding tooltip for new users - client component wrapper */}
      <ClientOnboardingWrapper />
    </Suspense>
  );
}
